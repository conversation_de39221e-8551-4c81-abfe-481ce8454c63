// Service-specific types and interfaces
import type { Database } from '@/integrations/supabase/types';

// Database table types
export type JobRow = Database['public']['Tables']['jobs']['Row'];
export type JobInsert = Database['public']['Tables']['jobs']['Insert'];
export type JobUpdate = Database['public']['Tables']['jobs']['Update'];

export type CompanyRow = Database['public']['Tables']['companies']['Row'];
export type CompanyInsert = Database['public']['Tables']['companies']['Insert'];
export type CompanyUpdate = Database['public']['Tables']['companies']['Update'];

export type JobApplicationRow = Database['public']['Tables']['job_applications']['Row'];
export type JobApplicationInsert = Database['public']['Tables']['job_applications']['Insert'];
export type JobApplicationUpdate = Database['public']['Tables']['job_applications']['Update'];

export type ProfileRow = Database['public']['Tables']['profiles']['Row'];
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

// Extended types with relations
export interface JobWithCompany extends JobRow {
  company?: CompanyRow;
}

export interface JobApplicationWithDetails extends JobApplicationRow {
  job?: JobWithCompany;
  profile?: ProfileRow;
}

// Service request/response types
export interface CreateJobRequest {
  title: string;
  company: string;
  location: string;
  salaryMin?: number;
  salaryMax?: number;
  jobType: string;
  category: string;
  isRemote?: boolean;
  description: string;
  requirements?: string;
  benefits?: string;
  tags?: string[];
  contactEmail?: string;
  companyWebsite?: string;
}

export interface UpdateJobRequest extends Partial<CreateJobRequest> {
  id: string;
  status?: 'active' | 'inactive' | 'closed';
}

export interface JobFilters {
  search?: string;
  location?: string;
  jobType?: string;
  category?: string;
  isRemote?: boolean;
  salaryMin?: number;
  salaryMax?: number;
  tags?: string[];
}

export interface CreateJobApplicationRequest {
  jobId: string;
  coverLetter?: string;
  resumeUrl?: string;
}

export interface UpdateJobApplicationRequest {
  id: string;
  status?: 'pending' | 'reviewed' | 'accepted' | 'rejected';
  coverLetter?: string;
  resumeUrl?: string;
}

export interface CreateCompanyRequest {
  name: string;
  description?: string;
  website?: string;
  logoUrl?: string;
  location?: string;
}

export interface UpdateCompanyRequest extends Partial<CreateCompanyRequest> {
  id: string;
}

export interface UpdateProfileRequest {
  fullName?: string;
  email?: string;
  phone?: string;
  location?: string;
  bio?: string;
  skills?: string[];
  experienceYears?: number;
  resumeUrl?: string;
  avatarUrl?: string;
}

// API Response types
export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Error types
export class ServiceError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'ServiceError';
  }
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}
