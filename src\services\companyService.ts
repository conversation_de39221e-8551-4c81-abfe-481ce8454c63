import { supabase } from '@/integrations/supabase/client';
import type { 
  CompanyRow, 
  CompanyInsert, 
  CompanyUpdate,
  CreateCompanyRequest,
  UpdateCompanyRequest,
  ServiceResponse,
  PaginatedResponse,
  ServiceError 
} from './types';

class CompanyService {
  /**
   * Get all companies with optional pagination
   */
  async getCompanies(
    page: number = 1,
    pageSize: number = 10,
    search?: string
  ): Promise<ServiceResponse<PaginatedResponse<CompanyRow>>> {
    try {
      let query = supabase
        .from('companies')
        .select('*', { count: 'exact' })
        .order('name', { ascending: true });

      // Apply search filter
      if (search) {
        query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
      }

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const totalPages = count ? Math.ceil(count / pageSize) : 0;

      return {
        data: {
          data: data || [],
          count: count || 0,
          page,
          pageSize,
          totalPages,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch companies',
        success: false,
      };
    }
  }

  /**
   * Get a single company by ID
   */
  async getCompanyById(id: string): Promise<ServiceResponse<CompanyRow>> {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch company',
        success: false,
      };
    }
  }

  /**
   * Get a company by name
   */
  async getCompanyByName(name: string): Promise<ServiceResponse<CompanyRow>> {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .eq('name', name)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: data || null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch company',
        success: false,
      };
    }
  }

  /**
   * Create a new company
   */
  async createCompany(companyData: CreateCompanyRequest): Promise<ServiceResponse<CompanyRow>> {
    try {
      // Validate required fields
      const validation = this.validateCompanyData(companyData);
      if (!validation.isValid) {
        throw new ServiceError(
          `Validation failed: ${validation.errors.map(e => e.message).join(', ')}`
        );
      }

      // Check if company with same name already exists
      const existingCompany = await this.getCompanyByName(companyData.name);
      if (existingCompany.success && existingCompany.data) {
        throw new ServiceError('A company with this name already exists');
      }

      const companyInsert: CompanyInsert = {
        name: companyData.name,
        description: companyData.description || null,
        website: companyData.website || null,
        logo_url: companyData.logoUrl || null,
        location: companyData.location || null,
      };

      const { data, error } = await supabase
        .from('companies')
        .insert(companyInsert)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create company',
        success: false,
      };
    }
  }

  /**
   * Update an existing company
   */
  async updateCompany(companyData: UpdateCompanyRequest): Promise<ServiceResponse<CompanyRow>> {
    try {
      const { id, ...updateData } = companyData;

      // If name is being updated, check for duplicates
      if (updateData.name) {
        const existingCompany = await this.getCompanyByName(updateData.name);
        if (existingCompany.success && existingCompany.data && existingCompany.data.id !== id) {
          throw new ServiceError('A company with this name already exists');
        }
      }

      const companyUpdate: CompanyUpdate = {
        ...(updateData.name && { name: updateData.name }),
        ...(updateData.description !== undefined && { description: updateData.description }),
        ...(updateData.website !== undefined && { website: updateData.website }),
        ...(updateData.logoUrl !== undefined && { logo_url: updateData.logoUrl }),
        ...(updateData.location !== undefined && { location: updateData.location }),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('companies')
        .update(companyUpdate)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to update company',
        success: false,
      };
    }
  }

  /**
   * Delete a company
   */
  async deleteCompany(id: string): Promise<ServiceResponse<null>> {
    try {
      // Check if company has associated jobs
      const { data: jobs, error: jobsError } = await supabase
        .from('jobs')
        .select('id')
        .eq('company_id', id)
        .limit(1);

      if (jobsError) {
        throw new ServiceError(jobsError.message, jobsError.code);
      }

      if (jobs && jobs.length > 0) {
        throw new ServiceError('Cannot delete company with associated jobs');
      }

      const { error } = await supabase
        .from('companies')
        .delete()
        .eq('id', id);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to delete company',
        success: false,
      };
    }
  }

  /**
   * Get jobs for a specific company
   */
  async getCompanyJobs(
    companyId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<PaginatedResponse<any>>> {
    try {
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;

      const { data, error, count } = await supabase
        .from('jobs')
        .select('*', { count: 'exact' })
        .eq('company_id', companyId)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const totalPages = count ? Math.ceil(count / pageSize) : 0;

      return {
        data: {
          data: data || [],
          count: count || 0,
          page,
          pageSize,
          totalPages,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch company jobs',
        success: false,
      };
    }
  }

  /**
   * Get company statistics
   */
  async getCompanyStats(companyId: string): Promise<ServiceResponse<{
    totalJobs: number;
    activeJobs: number;
    totalApplications: number;
  }>> {
    try {
      // Get job counts
      const { data: jobs, error: jobsError } = await supabase
        .from('jobs')
        .select('id, status')
        .eq('company_id', companyId);

      if (jobsError) {
        throw new ServiceError(jobsError.message, jobsError.code);
      }

      const totalJobs = jobs?.length || 0;
      const activeJobs = jobs?.filter(job => job.status === 'active').length || 0;

      // Get application counts for company jobs
      const jobIds = jobs?.map(job => job.id) || [];
      let totalApplications = 0;

      if (jobIds.length > 0) {
        const { data: applications, error: applicationsError } = await supabase
          .from('job_applications')
          .select('id')
          .in('job_id', jobIds);

        if (applicationsError) {
          throw new ServiceError(applicationsError.message, applicationsError.code);
        }

        totalApplications = applications?.length || 0;
      }

      return {
        data: {
          totalJobs,
          activeJobs,
          totalApplications,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch company statistics',
        success: false,
      };
    }
  }

  /**
   * Validate company data
   */
  private validateCompanyData(companyData: CreateCompanyRequest) {
    const errors: Array<{ field: string; message: string }> = [];

    if (!companyData.name?.trim()) {
      errors.push({ field: 'name', message: 'Company name is required' });
    }

    if (companyData.website && !this.isValidUrl(companyData.website)) {
      errors.push({ field: 'website', message: 'Please enter a valid website URL' });
    }

    if (companyData.logoUrl && !this.isValidUrl(companyData.logoUrl)) {
      errors.push({ field: 'logoUrl', message: 'Please enter a valid logo URL' });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

export const companyService = new CompanyService();
