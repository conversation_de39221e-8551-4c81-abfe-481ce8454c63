import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { profileService } from "@/services/profileService";
import { useAuth } from "@/components/AuthProvider";
import { useToast } from "@/hooks/use-toast";
import type { UpdateProfileRequest } from "@/services/types";

export const useProfile = (userId?: string) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  return useQuery({
    queryKey: ['profile', targetUserId],
    queryFn: async () => {
      if (!targetUserId) {
        throw new Error("User ID is required");
      }

      const result = await profileService.getProfile(targetUserId);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch profile');
      }

      return result.data;
    },
    enabled: !!targetUserId,
  });
};

export const useCurrentUserProfile = () => {
  const { user } = useAuth();
  return useProfile(user?.id);
};

export const useUpdateProfile = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (profileData: UpdateProfileRequest) => {
      if (!user) {
        throw new Error("User must be logged in");
      }

      const result = await profileService.updateProfile(user.id, profileData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Profile updated!",
        description: "Your profile has been successfully updated.",
      });
      queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
    },
    onError: (error: any) => {
      toast({
        title: "Update failed",
        description: error.message || "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useCreateProfile = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (profileData: Partial<UpdateProfileRequest>) => {
      if (!user) {
        throw new Error("User must be logged in");
      }

      const result = await profileService.createProfile(user.id, profileData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create profile');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Profile created!",
        description: "Your profile has been successfully created.",
      });
      queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
    },
    onError: (error: any) => {
      toast({
        title: "Creation failed",
        description: error.message || "Failed to create profile. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUploadAvatar = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      if (!user) {
        throw new Error("User must be logged in");
      }

      const result = await profileService.uploadAvatar(user.id, file);

      if (!result.success) {
        throw new Error(result.error || 'Failed to upload avatar');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Avatar uploaded!",
        description: "Your avatar has been successfully uploaded.",
      });
      queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
    },
    onError: (error: any) => {
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload avatar. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUploadResume = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      if (!user) {
        throw new Error("User must be logged in");
      }

      const result = await profileService.uploadResume(user.id, file);

      if (!result.success) {
        throw new Error(result.error || 'Failed to upload resume');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Resume uploaded!",
        description: "Your resume has been successfully uploaded.",
      });
      queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
    },
    onError: (error: any) => {
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload resume. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useSearchProfiles = (query: string, page: number = 1, pageSize: number = 10) => {
  return useQuery({
    queryKey: ['search-profiles', query, page, pageSize],
    queryFn: async () => {
      const result = await profileService.searchProfiles(query, page, pageSize);

      if (!result.success) {
        throw new Error(result.error || 'Failed to search profiles');
      }

      return result.data;
    },
    enabled: !!query,
  });
};

export const useProfileCompletion = () => {
  const { user } = useAuth();
  const { data: profile } = useProfile(user?.id);

  if (!profile) {
    return 0;
  }

  return profileService.getProfileCompletion(profile);
};
