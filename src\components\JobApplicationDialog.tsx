
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useJobApplication } from '@/hooks/useJobApplication';
import { useAuth } from '@/components/AuthProvider';
import { Link } from 'react-router-dom';

interface JobApplicationDialogProps {
  jobId: string;
  jobTitle: string;
  companyName: string;
}

export const JobApplicationDialog = ({ jobId, jobTitle, companyName }: JobApplicationDialogProps) => {
  const [open, setOpen] = useState(false);
  const [coverLetter, setCoverLetter] = useState('');
  const { user } = useAuth();
  const jobApplication = useJobApplication();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    jobApplication.mutate(
      { jobId, coverLetter },
      {
        onSuccess: () => {
          setOpen(false);
          setCoverLetter('');
        },
      }
    );
  };

  if (!user) {
    return (
      <Link to="/auth">
        <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 h-12">
          Sign In to Apply
        </Button>
      </Link>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 h-12">
          Apply Now
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Apply for {jobTitle}</DialogTitle>
          <DialogDescription>
            Applying to {companyName}. Your profile information will be submitted with this application.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="coverLetter">Cover Letter (Optional)</Label>
            <Textarea
              id="coverLetter"
              placeholder="Tell us why you're interested in this position..."
              value={coverLetter}
              onChange={(e) => setCoverLetter(e.target.value)}
              rows={6}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={jobApplication.isPending}>
              {jobApplication.isPending ? 'Submitting...' : 'Submit Application'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
