const express = require('express');
const router = express.Router();
const profileController = require('../controllers/profileController');
const { verifyToken } = require('../middleware/auth');
const { validateProfileUpdate, validatePagination } = require('../middleware/validation');
const { uploadAvatar, uploadResume, handleUploadError } = require('../utils/fileUpload');

// @route   GET /api/profiles/me
// @desc    Get current user's profile
// @access  Private
router.get('/me', verifyToken, profileController.getCurrentUserProfile);

// @route   GET /api/profiles/:userId
// @desc    Get user profile by ID
// @access  Private
router.get('/:userId', verifyToken, profileController.getProfile);

// @route   PUT /api/profiles/me
// @desc    Update current user's profile
// @access  Private
router.put('/me', verifyToken, validateProfileUpdate, profileController.updateProfile);

// @route   POST /api/profiles/upload-avatar
// @desc    Upload profile avatar
// @access  Private
router.post('/upload-avatar', verifyToken, uploadAvatar, handleUploadError, profileController.uploadAvatar);

// @route   POST /api/profiles/upload-resume
// @desc    Upload resume
// @access  Private
router.post('/upload-resume', verifyToken, uploadResume, handleUploadError, profileController.uploadResume);

// @route   GET /api/profiles/search
// @desc    Search profiles
// @access  Private
router.get('/search', verifyToken, validatePagination, profileController.searchProfiles);

// @route   GET /api/profiles/me/completion
// @desc    Get profile completion percentage
// @access  Private
router.get('/me/completion', verifyToken, profileController.getProfileCompletion);

// @route   DELETE /api/profiles/me
// @desc    Delete current user's profile
// @access  Private
router.delete('/me', verifyToken, profileController.deleteProfile);

module.exports = router;
