const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import database connection
const { connectDatabase, healthCheck } = require('./config/database');

// Import routes
const authRoutes = require('./routes/auth');
const jobRoutes = require('./routes/jobs');
const applicationRoutes = require('./routes/applications');
const companyRoutes = require('./routes/companies');
const profileRoutes = require('./routes/profiles');

// Import utilities
const { verifyEmailConfig } = require('./utils/emailService');
const { verifyCloudinaryConfig } = require('./utils/fileUpload');

// Create Express app
const app = express();

// Trust proxy (important for rate limiting behind reverse proxy)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// Compression middleware
app.use(compression());

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
    
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const dbHealth = await healthCheck();

    res.json({
      success: true,
      message: 'Server is healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: dbHealth,
      uptime: process.uptime(),
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server health check failed',
      error: error.message
    });
  }
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/jobs', jobRoutes);
app.use('/api/applications', applicationRoutes);
app.use('/api/companies', companyRoutes);
app.use('/api/profiles', profileRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Career Link Spark API',
    version: '1.0.0',
    documentation: '/api/docs',
    endpoints: {
      auth: '/api/auth',
      jobs: '/api/jobs',
      applications: '/api/applications',
      companies: '/api/companies',
      profiles: '/api/profiles'
    }
  });
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    success: true,
    message: 'Career Link Spark API Documentation',
    version: '1.0.0',
    endpoints: {
      auth: {
        'POST /api/auth/register': 'Register new user',
        'POST /api/auth/login': 'Login user',
        'POST /api/auth/refresh-token': 'Refresh access token',
        'GET /api/auth/me': 'Get current user',
        'POST /api/auth/logout': 'Logout user',
        'POST /api/auth/forgot-password': 'Send password reset email',
        'POST /api/auth/reset-password': 'Reset password with token',
        'POST /api/auth/change-password': 'Change password',
        'POST /api/auth/verify-email': 'Verify email address'
      },
      jobs: {
        'GET /api/jobs': 'Get all jobs with filtering',
        'GET /api/jobs/featured': 'Get featured jobs',
        'GET /api/jobs/:id': 'Get single job',
        'POST /api/jobs': 'Create new job (Employer)',
        'PUT /api/jobs/:id': 'Update job (Owner)',
        'DELETE /api/jobs/:id': 'Delete job (Owner)',
        'GET /api/jobs/user/my-jobs': 'Get user\'s posted jobs',
        'GET /api/jobs/:id/stats': 'Get job statistics'
      },
      applications: {
        'POST /api/applications': 'Submit job application',
        'GET /api/applications/my-applications': 'Get user applications',
        'GET /api/applications/my-stats': 'Get user application stats',
        'GET /api/applications/job/:jobId': 'Get job applications (Employer)',
        'PUT /api/applications/:id/status': 'Update application status',
        'GET /api/applications/:id': 'Get application details',
        'DELETE /api/applications/:id': 'Withdraw application'
      },
      companies: {
        'GET /api/companies': 'Get all companies',
        'GET /api/companies/:id': 'Get single company',
        'POST /api/companies': 'Create company (Employer)',
        'PUT /api/companies/:id': 'Update company (Admin)',
        'DELETE /api/companies/:id': 'Delete company (Admin)',
        'GET /api/companies/:id/jobs': 'Get company jobs',
        'GET /api/companies/:id/stats': 'Get company statistics'
      },
      profiles: {
        'GET /api/profiles/me': 'Get current user profile',
        'GET /api/profiles/:userId': 'Get user profile',
        'PUT /api/profiles/me': 'Update profile',
        'POST /api/profiles/upload-avatar': 'Upload avatar',
        'POST /api/profiles/upload-resume': 'Upload resume',
        'GET /api/profiles/search': 'Search profiles',
        'GET /api/profiles/me/completion': 'Get profile completion',
        'DELETE /api/profiles/me': 'Delete profile'
      }
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);

  // Handle specific error types
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: error.errors
    });
  }

  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized access'
    });
  }

  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      message: 'File too large'
    });
  }

  // Default error response
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);
  
  try {
    // Close database connections
    const { closeConnections } = require('./config/database');
    await closeConnections();
    
    console.log('✅ Database connections closed');
    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('UNHANDLED_REJECTION');
});

// Start server
const PORT = process.env.PORT || 5000;

const startServer = async () => {
  try {
    // Verify configurations
    console.log('🔧 Verifying configurations...');
    
    // Connect to database
    await connectDatabase();
    
    // Verify email configuration (optional)
    if (process.env.EMAIL_HOST) {
      await verifyEmailConfig();
    } else {
      console.log('⚠️  Email configuration not found - email features will be disabled');
    }
    
    // Verify Cloudinary configuration (optional)
    if (process.env.CLOUDINARY_CLOUD_NAME) {
      verifyCloudinaryConfig();
    } else {
      console.log('⚠️  Cloudinary configuration not found - file upload features will be disabled');
    }
    
    // Start the server
    app.listen(PORT, () => {
      console.log(`\n🚀 Server running on port ${PORT}`);
      console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🌐 API URL: http://localhost:${PORT}`);
      console.log(`📚 Documentation: http://localhost:${PORT}/api/docs`);
      console.log(`❤️  Health Check: http://localhost:${PORT}/health`);
      console.log('\n✅ Career Link Spark API is ready!\n');
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = app;
