const { Pool } = require('pg');
const mysql = require('mysql2/promise');
const { MongoClient } = require('mongodb');
require('dotenv').config();

// Database configuration based on environment
const dbConfig = {
  // PostgreSQL configuration
  postgres: {
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  },
  
  // MySQL configuration
  mysql: {
    host: process.env.MYSQL_HOST || 'localhost',
    port: process.env.MYSQL_PORT || 3306,
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
  },
  
  // MongoDB configuration
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/career_link_spark',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    },
  },
};

// Database connection instances
let pgPool = null;
let mysqlPool = null;
let mongoClient = null;
let mongoDb = null;

// PostgreSQL connection
const connectPostgreSQL = async () => {
  try {
    if (!pgPool) {
      pgPool = new Pool(dbConfig.postgres);
    }
    
    // Test connection
    const client = await pgPool.connect();
    console.log('✅ PostgreSQL connected successfully');
    client.release();
    
    return pgPool;
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
    throw error;
  }
};

// MySQL connection
const connectMySQL = async () => {
  try {
    if (!mysqlPool) {
      mysqlPool = mysql.createPool(dbConfig.mysql);
    }
    
    // Test connection
    const connection = await mysqlPool.getConnection();
    console.log('✅ MySQL connected successfully');
    connection.release();
    
    return mysqlPool;
  } catch (error) {
    console.error('❌ MySQL connection failed:', error.message);
    throw error;
  }
};

// MongoDB connection
const connectMongoDB = async () => {
  try {
    if (!mongoClient) {
      mongoClient = new MongoClient(dbConfig.mongodb.uri, dbConfig.mongodb.options);
      await mongoClient.connect();
      mongoDb = mongoClient.db();
    }
    
    console.log('✅ MongoDB connected successfully');
    return mongoDb;
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    throw error;
  }
};

// Generic database connection based on environment
const connectDatabase = async () => {
  const dbType = process.env.DB_TYPE || 'postgres';
  
  switch (dbType.toLowerCase()) {
    case 'postgres':
    case 'postgresql':
      return await connectPostgreSQL();
    case 'mysql':
      return await connectMySQL();
    case 'mongodb':
    case 'mongo':
      return await connectMongoDB();
    default:
      throw new Error(`Unsupported database type: ${dbType}`);
  }
};

// Database query helpers
const executeQuery = async (query, params = []) => {
  const dbType = process.env.DB_TYPE || 'postgres';
  
  try {
    switch (dbType.toLowerCase()) {
      case 'postgres':
      case 'postgresql':
        if (!pgPool) await connectPostgreSQL();
        const pgResult = await pgPool.query(query, params);
        return pgResult.rows;
        
      case 'mysql':
        if (!mysqlPool) await connectMySQL();
        const [mysqlRows] = await mysqlPool.execute(query, params);
        return mysqlRows;
        
      default:
        throw new Error(`Query execution not supported for ${dbType}`);
    }
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};

// MongoDB collection helper
const getCollection = async (collectionName) => {
  if (!mongoDb) await connectMongoDB();
  return mongoDb.collection(collectionName);
};

// Close database connections
const closeConnections = async () => {
  try {
    if (pgPool) {
      await pgPool.end();
      pgPool = null;
      console.log('PostgreSQL connection closed');
    }
    
    if (mysqlPool) {
      await mysqlPool.end();
      mysqlPool = null;
      console.log('MySQL connection closed');
    }
    
    if (mongoClient) {
      await mongoClient.close();
      mongoClient = null;
      mongoDb = null;
      console.log('MongoDB connection closed');
    }
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
};

// Health check
const healthCheck = async () => {
  const dbType = process.env.DB_TYPE || 'postgres';
  
  try {
    switch (dbType.toLowerCase()) {
      case 'postgres':
      case 'postgresql':
        if (!pgPool) await connectPostgreSQL();
        await pgPool.query('SELECT 1');
        return { status: 'healthy', database: 'PostgreSQL' };
        
      case 'mysql':
        if (!mysqlPool) await connectMySQL();
        await mysqlPool.execute('SELECT 1');
        return { status: 'healthy', database: 'MySQL' };
        
      case 'mongodb':
      case 'mongo':
        if (!mongoDb) await connectMongoDB();
        await mongoDb.admin().ping();
        return { status: 'healthy', database: 'MongoDB' };
        
      default:
        throw new Error(`Health check not supported for ${dbType}`);
    }
  } catch (error) {
    return { status: 'unhealthy', database: dbType, error: error.message };
  }
};

module.exports = {
  connectDatabase,
  connectPostgreSQL,
  connectMySQL,
  connectMongoDB,
  executeQuery,
  getCollection,
  closeConnections,
  healthCheck,
  
  // Direct access to connection instances
  get pgPool() { return pgPool; },
  get mysqlPool() { return mysqlPool; },
  get mongoDb() { return mongoDb; },
};
