const express = require('express');
const router = express.Router();
const companyController = require('../controllers/companyController');
const { verifyToken, requireEmployer, requireAdmin } = require('../middleware/auth');
const {
  validateCompanyCreation,
  validatePagination
} = require('../middleware/validation');

// @route   GET /api/companies
// @desc    Get all companies with pagination and search
// @access  Public
router.get('/', validatePagination, companyController.getCompanies);

// @route   GET /api/companies/:id
// @desc    Get single company by ID
// @access  Public
router.get('/:id', companyController.getCompanyById);

// @route   POST /api/companies
// @desc    Create new company
// @access  Private (Employer/Admin)
router.post('/', verifyToken, requireEmployer, validateCompanyCreation, companyController.createCompany);

// @route   PUT /api/companies/:id
// @desc    Update company
// @access  Private (Admin only)
router.put('/:id', verifyToken, requireAdmin, companyController.updateCompany);

// @route   DELETE /api/companies/:id
// @desc    Delete company
// @access  Private (Admin only)
router.delete('/:id', verifyToken, requireAdmin, companyController.deleteCompany);

// @route   GET /api/companies/:id/jobs
// @desc    Get jobs for a specific company
// @access  Public
router.get('/:id/jobs', validatePagination, companyController.getCompanyJobs);

// @route   GET /api/companies/:id/stats
// @desc    Get company statistics
// @access  Public
router.get('/:id/stats', companyController.getCompanyStats);

module.exports = router;
