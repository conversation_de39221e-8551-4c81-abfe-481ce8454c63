#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, args, cwd, description) {
  return new Promise((resolve, reject) => {
    log(`\n📦 ${description}...`, 'cyan');
    
    const child = spawn(command, args, {
      cwd,
      stdio: 'inherit',
      shell: true
    });

    child.on('close', (code) => {
      if (code === 0) {
        log(`✅ ${description} completed`, 'green');
        resolve();
      } else {
        log(`❌ ${description} failed`, 'red');
        reject(new Error(`${description} failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      log(`❌ Error during ${description}: ${error.message}`, 'red');
      reject(error);
    });
  });
}

async function setupProject() {
  try {
    log('🚀 Career Link Spark - Project Setup', 'bright');
    log('====================================', 'bright');

    // Check if we're in the right directory
    if (!fs.existsSync('backend') || !fs.existsSync('src')) {
      log('❌ Please run this script from the project root directory', 'red');
      log('Expected structure: backend/ and src/ folders', 'red');
      process.exit(1);
    }

    // Install frontend dependencies
    await runCommand('npm', ['install'], '.', 'Installing frontend dependencies');

    // Install backend dependencies
    await runCommand('npm', ['install'], './backend', 'Installing backend dependencies');

    // Setup environment files
    log('\n⚙️ Setting up environment files...', 'yellow');
    
    // Frontend .env
    if (!fs.existsSync('.env')) {
      if (fs.existsSync('.env.example')) {
        fs.copyFileSync('.env.example', '.env');
        log('✅ Frontend .env file created from .env.example', 'green');
      } else {
        log('⚠️  Frontend .env.example not found, skipping', 'yellow');
      }
    } else {
      log('✅ Frontend .env file already exists', 'green');
    }

    // Backend .env
    if (!fs.existsSync('backend/.env')) {
      if (fs.existsSync('backend/.env.example')) {
        fs.copyFileSync('backend/.env.example', 'backend/.env');
        log('✅ Backend .env file created from .env.example', 'green');
        log('⚠️  Please edit backend/.env with your database configuration', 'yellow');
      } else {
        log('❌ Backend .env.example not found', 'red');
        log('Please create backend/.env manually', 'red');
      }
    } else {
      log('✅ Backend .env file already exists', 'green');
    }

    log('\n🎉 Project setup completed!', 'bright');
    log('\n📋 Next steps:', 'yellow');
    log('1. Edit backend/.env with your database configuration', 'cyan');
    log('2. Setup your database (PostgreSQL, MySQL, or MongoDB)', 'cyan');
    log('3. Run: npm run backend:setup (to create tables and sample data)', 'cyan');
    log('4. Run: npm run dev:fullstack (to start both servers)', 'cyan');
    log('\n📚 For detailed instructions, see RUN_FULLSTACK.md', 'blue');

  } catch (error) {
    log(`\n❌ Setup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run setup
setupProject();
