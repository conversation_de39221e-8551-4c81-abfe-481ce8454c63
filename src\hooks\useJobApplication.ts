
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/AuthProvider";
import { useToast } from "@/hooks/use-toast";

interface JobApplicationData {
  jobId: string;
  coverLetter?: string;
}

export const useJobApplication = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ jobId, coverLetter }: JobApplicationData) => {
      if (!user) {
        throw new Error("User must be logged in to apply");
      }

      const { data, error } = await supabase
        .from('job_applications')
        .insert({
          job_id: jobId,
          user_id: user.id,
          applicant_name: user.user_metadata?.full_name || user.email || 'Unknown',
          applicant_email: user.email || '',
          cover_letter: coverLetter,
          status: 'pending'
        });

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      toast({
        title: "Application submitted!",
        description: "Your job application has been successfully submitted.",
      });
      queryClient.invalidateQueries({ queryKey: ['job-applications'] });
    },
    onError: (error: any) => {
      console.error('Application error:', error);
      if (error.message?.includes('duplicate key')) {
        toast({
          title: "Already applied",
          description: "You have already applied for this job.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Application failed",
          description: error.message || "Failed to submit application. Please try again.",
          variant: "destructive",
        });
      }
    },
  });
};
