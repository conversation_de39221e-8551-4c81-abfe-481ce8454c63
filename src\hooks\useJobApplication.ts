
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { jobApplicationService } from "@/services/jobApplicationService";
import { useAuth } from "@/components/AuthProvider";
import { useToast } from "@/hooks/use-toast";
import type { CreateJobApplicationRequest, UpdateJobApplicationRequest } from "@/services/types";

export const useJobApplication = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (applicationData: CreateJobApplicationRequest) => {
      if (!user) {
        throw new Error("User must be logged in to apply");
      }

      const result = await jobApplicationService.createApplication(
        applicationData,
        user.id,
        user.email || '',
        user.user_metadata?.full_name || user.email || 'Unknown'
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to submit application');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Application submitted!",
        description: "Your job application has been successfully submitted.",
      });
      queryClient.invalidateQueries({ queryKey: ['job-applications'] });
      queryClient.invalidateQueries({ queryKey: ['user-applications'] });
    },
    onError: (error: any) => {
      toast({
        title: "Application failed",
        description: error.message || "Failed to submit your application. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateJobApplication = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (applicationData: UpdateJobApplicationRequest) => {
      const result = await jobApplicationService.updateApplication(applicationData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update application');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Application updated!",
        description: "Your job application has been successfully updated.",
      });
      queryClient.invalidateQueries({ queryKey: ['job-applications'] });
      queryClient.invalidateQueries({ queryKey: ['user-applications'] });
    },
    onError: (error: any) => {
      toast({
        title: "Update failed",
        description: error.message || "Failed to update your application. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUserApplications = (page: number = 1, pageSize: number = 10) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-applications', user?.id, page, pageSize],
    queryFn: async () => {
      if (!user) {
        throw new Error("User must be logged in");
      }

      const result = await jobApplicationService.getApplicationsByUser(user.id, page, pageSize);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch applications');
      }

      return result.data;
    },
    enabled: !!user,
  });
};

export const useJobApplications = (jobId: string, page: number = 1, pageSize: number = 10) => {
  return useQuery({
    queryKey: ['job-applications', jobId, page, pageSize],
    queryFn: async () => {
      const result = await jobApplicationService.getApplicationsByJob(jobId, page, pageSize);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch job applications');
      }

      return result.data;
    },
    enabled: !!jobId,
  });
};

export const useJobApplicationStats = (jobId: string) => {
  return useQuery({
    queryKey: ['job-application-stats', jobId],
    queryFn: async () => {
      const result = await jobApplicationService.getJobApplicationStats(jobId);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch application statistics');
      }

      return result.data;
    },
    enabled: !!jobId,
  });
};

export const useUserApplicationStats = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-application-stats', user?.id],
    queryFn: async () => {
      if (!user) {
        throw new Error("User must be logged in");
      }

      const result = await jobApplicationService.getUserApplicationStats(user.id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch application statistics');
      }

      return result.data;
    },
    enabled: !!user,
  });
};
