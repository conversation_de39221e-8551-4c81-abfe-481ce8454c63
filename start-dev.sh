#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "  Career Link Spark - Development"
echo -e "========================================${NC}"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo -e "${GREEN}✅ Node.js is installed${NC}"

# Check if we're in the right directory
if [ ! -d "backend" ]; then
    echo -e "${RED}❌ Backend folder not found${NC}"
    echo "Please run this script from the project root directory"
    exit 1
fi

if [ ! -d "src" ]; then
    echo -e "${RED}❌ Frontend src folder not found${NC}"
    echo "Please run this script from the project root directory"
    exit 1
fi

echo -e "${GREEN}✅ Project structure verified${NC}"

# Install frontend dependencies if needed
if [ ! -d "node_modules" ]; then
    echo
    echo -e "${CYAN}📦 Installing frontend dependencies...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to install frontend dependencies${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Frontend dependencies already installed${NC}"
fi

# Install backend dependencies if needed
if [ ! -d "backend/node_modules" ]; then
    echo
    echo -e "${CYAN}📦 Installing backend dependencies...${NC}"
    cd backend
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to install backend dependencies${NC}"
        exit 1
    fi
    cd ..
else
    echo -e "${GREEN}✅ Backend dependencies already installed${NC}"
fi

# Check if backend .env exists
if [ ! -f "backend/.env" ]; then
    echo
    echo -e "${YELLOW}⚠️  Backend .env file not found!${NC}"
    echo "Please copy .env.example to .env and configure it:"
    echo
    echo -e "${CYAN}  cd backend${NC}"
    echo -e "${CYAN}  cp .env.example .env${NC}"
    echo
    echo "Then edit the .env file with your database and service configurations."
    echo
    exit 1
fi

echo -e "${GREEN}✅ Backend .env file found${NC}"

echo
echo -e "${BLUE}🚀 Starting development servers...${NC}"
echo
echo -e "${CYAN}Frontend: http://localhost:5173${NC}"
echo -e "${MAGENTA}Backend:  http://localhost:5000${NC}"
echo -e "${MAGENTA}API Docs: http://localhost:5000/api/docs${NC}"
echo
echo -e "${YELLOW}Press Ctrl+C to stop both servers${NC}"
echo

# Make the script executable and start both servers
chmod +x run-dev.js
node run-dev.js
