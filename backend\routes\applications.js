const express = require('express');
const router = express.Router();
const jobApplicationController = require('../controllers/jobApplicationController');
const { verifyToken, requireEmployer } = require('../middleware/auth');
const {
  validateJobApplication,
  validateApplicationStatusUpdate,
  validatePagination
} = require('../middleware/validation');

// @route   POST /api/applications
// @desc    Submit job application
// @access  Private
router.post('/', verifyToken, validateJobApplication, jobApplicationController.createApplication);

// @route   GET /api/applications/my-applications
// @desc    Get current user's applications
// @access  Private
router.get('/my-applications', verifyToken, validatePagination, jobApplicationController.getUserApplications);

// @route   GET /api/applications/my-stats
// @desc    Get current user's application statistics
// @access  Private
router.get('/my-stats', verifyToken, jobApplicationController.getUserApplicationStats);

// @route   GET /api/applications/job/:jobId
// @desc    Get applications for a specific job (for employers)
// @access  Private (Job Owner/Admin)
router.get('/job/:jobId', verifyToken, validatePagination, jobApplicationController.getJobApplications);

// @route   GET /api/applications/job/:jobId/stats
// @desc    Get application statistics for a job
// @access  Private (Job Owner/Admin)
router.get('/job/:jobId/stats', verifyToken, jobApplicationController.getJobApplicationStats);

// @route   PUT /api/applications/:id/status
// @desc    Update application status
// @access  Private (Job Owner/Admin)
router.put('/:id/status', verifyToken, validateApplicationStatusUpdate, jobApplicationController.updateApplicationStatus);

// @route   GET /api/applications/:id
// @desc    Get single application details
// @access  Private (Application Owner/Job Owner/Admin)
router.get('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { executeQuery, getCollection } = require('../config/database');
    const dbType = process.env.DB_TYPE || 'postgres';

    let application;

    if (dbType === 'mongodb') {
      const applicationsCollection = await getCollection('job_applications');
      const jobsCollection = await getCollection('jobs');
      const profilesCollection = await getCollection('profiles');

      application = await applicationsCollection.findOne({ _id: id });
      
      if (application) {
        // Get job details
        application.job = await jobsCollection.findOne({ _id: application.job_id });
        
        // Get profile details
        application.profile = await profilesCollection.findOne({ _id: application.user_id });
      }
    } else {
      const query = `
        SELECT ja.*, j.title as job_title, j.posted_by as job_owner,
               p.full_name, p.email, p.phone, p.location as profile_location,
               p.bio, p.skills, p.experience_years, p.avatar_url
        FROM job_applications ja
        LEFT JOIN jobs j ON ja.job_id = j.id
        LEFT JOIN profiles p ON ja.user_id = p.id
        WHERE ja.id = ?
      `;

      const applications = await executeQuery(query, [id]);
      application = applications[0];

      if (application) {
        // Format application with job and profile data
        application.job = {
          id: application.job_id,
          title: application.job_title,
          posted_by: application.job_owner
        };

        application.profile = application.full_name ? {
          id: application.user_id,
          full_name: application.full_name,
          email: application.email,
          phone: application.phone,
          location: application.profile_location,
          bio: application.bio,
          skills: application.skills,
          experience_years: application.experience_years,
          avatar_url: application.avatar_url
        } : null;
      }
    }

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    // Check authorization
    const isOwner = application.user_id === userId;
    const isJobOwner = application.job?.posted_by === userId;
    const isAdmin = req.user.role === 'admin';

    if (!isOwner && !isJobOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this application'
      });
    }

    res.json({
      success: true,
      data: { application }
    });
  } catch (error) {
    console.error('Get application error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   DELETE /api/applications/:id
// @desc    Delete/withdraw application
// @access  Private (Application Owner/Admin)
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { executeQuery, getCollection } = require('../config/database');
    const dbType = process.env.DB_TYPE || 'postgres';

    // Get application
    let application;
    if (dbType === 'mongodb') {
      const applicationsCollection = await getCollection('job_applications');
      application = await applicationsCollection.findOne({ _id: id });
    } else {
      const applications = await executeQuery('SELECT * FROM job_applications WHERE id = ?', [id]);
      application = applications[0];
    }

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    // Check authorization
    if (application.user_id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this application'
      });
    }

    // Delete application
    if (dbType === 'mongodb') {
      const applicationsCollection = await getCollection('job_applications');
      await applicationsCollection.deleteOne({ _id: id });
    } else {
      await executeQuery('DELETE FROM job_applications WHERE id = ?', [id]);
    }

    res.json({
      success: true,
      message: 'Application withdrawn successfully'
    });
  } catch (error) {
    console.error('Delete application error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   GET /api/applications/employer/dashboard
// @desc    Get employer dashboard data (all applications for their jobs)
// @access  Private (Employer/Admin)
router.get('/employer/dashboard', verifyToken, requireEmployer, async (req, res) => {
  try {
    const userId = req.user.id;
    const { executeQuery, getCollection } = require('../config/database');
    const dbType = process.env.DB_TYPE || 'postgres';

    let dashboardData;

    if (dbType === 'mongodb') {
      const jobsCollection = await getCollection('jobs');
      const applicationsCollection = await getCollection('job_applications');

      // Get user's jobs
      const userJobs = await jobsCollection
        .find({ posted_by: userId }, { projection: { _id: 1 } })
        .toArray();
      
      const jobIds = userJobs.map(job => job._id);

      if (jobIds.length === 0) {
        return res.json({
          success: true,
          data: {
            totalApplications: 0,
            pendingApplications: 0,
            recentApplications: [],
            applicationsByStatus: {
              pending: 0,
              reviewed: 0,
              accepted: 0,
              rejected: 0
            }
          }
        });
      }

      // Get application statistics
      const totalApplications = await applicationsCollection.countDocuments({
        job_id: { $in: jobIds }
      });

      const pendingApplications = await applicationsCollection.countDocuments({
        job_id: { $in: jobIds },
        status: 'pending'
      });

      // Get recent applications
      const recentApplications = await applicationsCollection
        .find({ job_id: { $in: jobIds } })
        .sort({ created_at: -1 })
        .limit(10)
        .toArray();

      // Get applications by status
      const statusPipeline = [
        { $match: { job_id: { $in: jobIds } } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ];

      const statusResults = await applicationsCollection.aggregate(statusPipeline).toArray();
      
      const applicationsByStatus = {
        pending: 0,
        reviewed: 0,
        accepted: 0,
        rejected: 0
      };

      statusResults.forEach(result => {
        applicationsByStatus[result._id] = result.count;
      });

      dashboardData = {
        totalApplications,
        pendingApplications,
        recentApplications,
        applicationsByStatus
      };
    } else {
      // Get application statistics
      const statsQuery = `
        SELECT 
          COUNT(*) as total_applications,
          COUNT(CASE WHEN ja.status = 'pending' THEN 1 END) as pending_applications
        FROM job_applications ja
        INNER JOIN jobs j ON ja.job_id = j.id
        WHERE j.posted_by = ?
      `;

      const statsResult = await executeQuery(statsQuery, [userId]);

      // Get recent applications
      const recentQuery = `
        SELECT ja.*, j.title as job_title
        FROM job_applications ja
        INNER JOIN jobs j ON ja.job_id = j.id
        WHERE j.posted_by = ?
        ORDER BY ja.created_at DESC
        LIMIT 10
      `;

      const recentApplications = await executeQuery(recentQuery, [userId]);

      // Get applications by status
      const statusQuery = `
        SELECT ja.status, COUNT(*) as count
        FROM job_applications ja
        INNER JOIN jobs j ON ja.job_id = j.id
        WHERE j.posted_by = ?
        GROUP BY ja.status
      `;

      const statusResults = await executeQuery(statusQuery, [userId]);
      
      const applicationsByStatus = {
        pending: 0,
        reviewed: 0,
        accepted: 0,
        rejected: 0
      };

      statusResults.forEach(result => {
        applicationsByStatus[result.status] = parseInt(result.count);
      });

      dashboardData = {
        totalApplications: parseInt(statsResult[0].total_applications),
        pendingApplications: parseInt(statsResult[0].pending_applications),
        recentApplications,
        applicationsByStatus
      };
    }

    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('Get employer dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
