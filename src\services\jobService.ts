import { supabase } from '@/integrations/supabase/client';
import type { 
  JobRow, 
  JobInsert, 
  JobUpdate, 
  JobWithCompany, 
  CreateJobRequest, 
  UpdateJobRequest, 
  JobFilters,
  ServiceResponse,
  PaginatedResponse,
  ServiceError 
} from './types';

class JobService {
  /**
   * Get all jobs with optional filtering and pagination
   */
  async getJobs(
    filters?: JobFilters,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<PaginatedResponse<JobWithCompany>>> {
    try {
      let query = supabase
        .from('jobs')
        .select(`
          *,
          company:companies(*)
        `, { count: 'exact' })
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters) {
        if (filters.search) {
          query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
        }
        
        if (filters.location) {
          query = query.ilike('location', `%${filters.location}%`);
        }
        
        if (filters.jobType) {
          query = query.eq('job_type', filters.jobType);
        }
        
        if (filters.category) {
          query = query.eq('category', filters.category);
        }
        
        if (filters.isRemote !== undefined) {
          query = query.eq('is_remote', filters.isRemote);
        }
        
        if (filters.salaryMin) {
          query = query.gte('salary_min', filters.salaryMin);
        }
        
        if (filters.salaryMax) {
          query = query.lte('salary_max', filters.salaryMax);
        }
        
        if (filters.tags && filters.tags.length > 0) {
          query = query.overlaps('tags', filters.tags);
        }
      }

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const totalPages = count ? Math.ceil(count / pageSize) : 0;

      return {
        data: {
          data: data || [],
          count: count || 0,
          page,
          pageSize,
          totalPages,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch jobs',
        success: false,
      };
    }
  }

  /**
   * Get a single job by ID
   */
  async getJobById(id: string): Promise<ServiceResponse<JobWithCompany>> {
    try {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          company:companies(*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch job',
        success: false,
      };
    }
  }

  /**
   * Create a new job
   */
  async createJob(jobData: CreateJobRequest): Promise<ServiceResponse<JobRow>> {
    try {
      // Validate required fields
      const validation = this.validateJobData(jobData);
      if (!validation.isValid) {
        throw new ServiceError(
          `Validation failed: ${validation.errors.map(e => e.message).join(', ')}`
        );
      }

      // First, create or get company
      let companyId: string | null = null;
      if (jobData.company) {
        const companyResult = await this.createOrGetCompany(jobData.company, jobData.companyWebsite);
        if (companyResult.success && companyResult.data) {
          companyId = companyResult.data.id;
        }
      }

      const jobInsert: JobInsert = {
        title: jobData.title,
        description: jobData.description,
        location: jobData.location,
        salary_min: jobData.salaryMin || null,
        salary_max: jobData.salaryMax || null,
        job_type: jobData.jobType,
        category: jobData.category,
        is_remote: jobData.isRemote || false,
        tags: jobData.tags || null,
        requirements: jobData.requirements || null,
        benefits: jobData.benefits || null,
        company_id: companyId,
        status: 'active',
      };

      const { data, error } = await supabase
        .from('jobs')
        .insert(jobInsert)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create job',
        success: false,
      };
    }
  }

  /**
   * Update an existing job
   */
  async updateJob(jobData: UpdateJobRequest): Promise<ServiceResponse<JobRow>> {
    try {
      const { id, ...updateData } = jobData;

      const jobUpdate: JobUpdate = {
        ...(updateData.title && { title: updateData.title }),
        ...(updateData.description && { description: updateData.description }),
        ...(updateData.location && { location: updateData.location }),
        ...(updateData.salaryMin !== undefined && { salary_min: updateData.salaryMin }),
        ...(updateData.salaryMax !== undefined && { salary_max: updateData.salaryMax }),
        ...(updateData.jobType && { job_type: updateData.jobType }),
        ...(updateData.category && { category: updateData.category }),
        ...(updateData.isRemote !== undefined && { is_remote: updateData.isRemote }),
        ...(updateData.tags && { tags: updateData.tags }),
        ...(updateData.requirements !== undefined && { requirements: updateData.requirements }),
        ...(updateData.benefits !== undefined && { benefits: updateData.benefits }),
        ...(updateData.status && { status: updateData.status }),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('jobs')
        .update(jobUpdate)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to update job',
        success: false,
      };
    }
  }

  /**
   * Delete a job
   */
  async deleteJob(id: string): Promise<ServiceResponse<null>> {
    try {
      const { error } = await supabase
        .from('jobs')
        .delete()
        .eq('id', id);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to delete job',
        success: false,
      };
    }
  }

  /**
   * Get featured jobs (latest 6 jobs)
   */
  async getFeaturedJobs(): Promise<ServiceResponse<JobWithCompany[]>> {
    try {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          company:companies(*)
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(6);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: data || [],
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch featured jobs',
        success: false,
      };
    }
  }

  /**
   * Create or get existing company
   */
  private async createOrGetCompany(name: string, website?: string) {
    try {
      // First, try to find existing company
      const { data: existingCompany } = await supabase
        .from('companies')
        .select('*')
        .eq('name', name)
        .single();

      if (existingCompany) {
        return {
          data: existingCompany,
          error: null,
          success: true,
        };
      }

      // Create new company
      const { data, error } = await supabase
        .from('companies')
        .insert({
          name,
          website: website || null,
        })
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create/get company',
        success: false,
      };
    }
  }

  /**
   * Validate job data
   */
  private validateJobData(jobData: CreateJobRequest) {
    const errors: Array<{ field: string; message: string }> = [];

    if (!jobData.title?.trim()) {
      errors.push({ field: 'title', message: 'Job title is required' });
    }

    if (!jobData.company?.trim()) {
      errors.push({ field: 'company', message: 'Company name is required' });
    }

    if (!jobData.description?.trim()) {
      errors.push({ field: 'description', message: 'Job description is required' });
    }

    if (!jobData.location?.trim()) {
      errors.push({ field: 'location', message: 'Location is required' });
    }

    if (!jobData.jobType?.trim()) {
      errors.push({ field: 'jobType', message: 'Job type is required' });
    }

    if (!jobData.category?.trim()) {
      errors.push({ field: 'category', message: 'Category is required' });
    }

    if (jobData.salaryMin && jobData.salaryMax && jobData.salaryMin > jobData.salaryMax) {
      errors.push({ field: 'salary', message: 'Minimum salary cannot be greater than maximum salary' });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

export const jobService = new JobService();
