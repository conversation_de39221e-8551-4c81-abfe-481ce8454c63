# 🚀 Running Career Link Spark Full Stack

This guide shows you how to run both the frontend (React) and backend (Node.js) together for development.

## 📋 Prerequisites

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **Database** (PostgreSQL, MySQL, or MongoDB)
- **Git** - [Download here](https://git-scm.com/)

## ⚡ Quick Start

### 1. **Install Dependencies**

**Frontend:**
```bash
npm install
```

**Backend:**
```bash
cd backend
npm install
cd ..
```

### 2. **Configure Backend**
```bash
cd backend
cp .env.example .env
# Edit .env with your database configuration
cd ..
```

### 3. **Setup Database**
```bash
cd backend
npm run setup  # This creates tables and adds sample data
cd ..
```

### 4. **Run Both Servers**

**Option A: Using the automated script (Recommended)**
```bash
npm run dev:fullstack
```

**Option B: Windows batch file**
```cmd
start-dev.bat
```

**Option C: Linux/Mac shell script**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

**Option D: Manual (separate terminals)**

Terminal 1 (Frontend):
```bash
npm run dev
```

Terminal 2 (Backend):
```bash
cd backend
npm run dev
```

## 🌐 Access Points

Once both servers are running:

- **Frontend Application**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api/docs
- **Health Check**: http://localhost:5000/health

## 🔧 Configuration

### Frontend Environment (.env)
```env
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=Career Link Spark
VITE_APP_VERSION=1.0.0
VITE_DEV_MODE=true
```

### Backend Environment (backend/.env)
```env
# Server
PORT=5000
NODE_ENV=development

# Database (choose one)
DB_TYPE=postgres
DATABASE_URL=postgresql://username:password@localhost:5432/career_link_spark

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Email (optional for development)
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload (optional)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Frontend
FRONTEND_URL=http://localhost:5173
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
```

## 📊 Sample Data

After running the backend setup, you can use these sample accounts:

- **Admin**: <EMAIL> / Admin123!
- **Employer**: <EMAIL> / Employer123!
- **User**: <EMAIL> / User123!

## 🛠️ Available Scripts

### Full Stack Scripts
```bash
npm run dev:fullstack     # Run both frontend and backend
npm run backend:setup     # Setup backend database
npm run backend:dev       # Run backend only
npm run backend:start     # Run backend in production mode
```

### Frontend Only Scripts
```bash
npm run dev              # Start development server
npm run build            # Build for production
npm run preview          # Preview production build
npm run lint             # Run linting
```

### Backend Only Scripts
```bash
cd backend
npm run dev              # Start development server
npm start                # Start production server
npm run migrate          # Run database migrations
npm run seed             # Add sample data
npm run setup            # Run migrations + seed data
npm test                 # Run tests
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Kill process using port 5000 (backend)
   npx kill-port 5000
   
   # Kill process using port 5173 (frontend)
   npx kill-port 5173
   ```

2. **Database Connection Error**
   - Check if your database server is running
   - Verify DATABASE_URL in backend/.env
   - Ensure database exists

3. **Module Not Found Errors**
   ```bash
   # Reinstall frontend dependencies
   rm -rf node_modules package-lock.json
   npm install
   
   # Reinstall backend dependencies
   cd backend
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **Backend .env Not Found**
   ```bash
   cd backend
   cp .env.example .env
   # Edit the .env file with your configuration
   ```

5. **Permission Denied (Linux/Mac)**
   ```bash
   chmod +x start-dev.sh
   chmod +x run-dev.js
   ```

### Development Tips

1. **API Testing**
   - Use the built-in API documentation: http://localhost:5000/api/docs
   - Test endpoints with tools like Postman or curl

2. **Database Management**
   ```bash
   cd backend
   npm run migrate      # Create/update tables
   npm run seed         # Add sample data
   ```

3. **Logs and Debugging**
   - Frontend logs appear in browser console
   - Backend logs appear in terminal
   - Check Network tab in browser dev tools for API calls

4. **Hot Reload**
   - Frontend automatically reloads on file changes
   - Backend automatically restarts on file changes (using nodemon)

## 🔄 Development Workflow

1. **Start Development Environment**
   ```bash
   npm run dev:fullstack
   ```

2. **Make Changes**
   - Edit frontend files in `src/`
   - Edit backend files in `backend/`
   - Both servers will automatically reload

3. **Test Your Changes**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000/api/docs

4. **Database Changes**
   ```bash
   cd backend
   npm run migrate  # Apply new migrations
   npm run seed     # Refresh sample data if needed
   ```

## 🚀 Production Build

### Frontend
```bash
npm run build
# Deploy the 'dist' folder to your hosting service
```

### Backend
```bash
cd backend
npm ci --only=production
npm start
# Or use PM2 for process management
```

## 📚 Project Structure

```
career-link-spark/
├── src/                    # Frontend React application
│   ├── components/         # React components
│   ├── pages/             # Page components
│   ├── services/          # API services
│   └── ...
├── backend/               # Backend Node.js API
│   ├── controllers/       # Route controllers
│   ├── routes/           # API routes
│   ├── middleware/       # Express middleware
│   ├── utils/            # Utility functions
│   └── ...
├── run-dev.js            # Development startup script
├── start-dev.sh          # Linux/Mac startup script
├── start-dev.bat         # Windows startup script
└── package.json          # Frontend dependencies
```

## 🆘 Getting Help

If you encounter issues:

1. Check the console/terminal for error messages
2. Verify all environment variables are set correctly
3. Ensure your database is running and accessible
4. Check that all dependencies are installed
5. Try restarting both servers

## 🎉 Success!

If everything is working correctly, you should see:

- ✅ Frontend running at http://localhost:5173
- ✅ Backend API running at http://localhost:5000
- ✅ Database connected and migrations completed
- ✅ Sample data loaded

You're now ready to develop the Career Link Spark job portal! 🚀

## 📝 Next Steps

1. **Explore the Application**
   - Visit http://localhost:5173
   - Try logging in with sample accounts
   - Browse jobs and test features

2. **API Development**
   - Check API documentation at http://localhost:5000/api/docs
   - Test endpoints with sample data

3. **Frontend Development**
   - Modify components in `src/`
   - Add new features and pages

4. **Backend Development**
   - Add new API endpoints in `backend/routes/`
   - Modify business logic in `backend/controllers/`
