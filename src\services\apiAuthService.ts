import type { ServiceResponse } from './types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

export interface ApiUser {
  id: string;
  email: string;
  fullName: string;
  role: 'user' | 'employer' | 'admin';
  isVerified?: boolean;
}

export interface SignUpData {
  email: string;
  password: string;
  fullName: string;
  role?: 'user' | 'employer';
}

export interface SignInData {
  email: string;
  password: string;
}

export interface ResetPasswordData {
  email: string;
}

export interface AuthResponse {
  user: ApiUser;
  token: string;
  refreshToken?: string;
}

class ApiAuthService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  /**
   * Sign up a new user
   */
  async signUp(data: SignUpData): Promise<ServiceResponse<AuthResponse>> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          fullName: data.fullName,
          role: data.role || 'user'
        })
      });

      const result = await response.json();

      if (!response.ok) {
        return { 
          data: null, 
          error: result.message || 'Sign up failed', 
          success: false 
        };
      }

      // Store tokens
      if (result.data?.token) {
        localStorage.setItem('auth_token', result.data.token);
        if (result.data?.refreshToken) {
          localStorage.setItem('refresh_token', result.data.refreshToken);
        }
      }

      return { 
        data: result.data, 
        error: null, 
        success: true 
      };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Sign up failed', 
        success: false 
      };
    }
  }

  /**
   * Sign in an existing user
   */
  async signIn(data: SignInData): Promise<ServiceResponse<AuthResponse>> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: data.email,
          password: data.password
        })
      });

      const result = await response.json();

      if (!response.ok) {
        return { 
          data: null, 
          error: result.message || 'Sign in failed', 
          success: false 
        };
      }

      // Store tokens
      if (result.data?.token) {
        localStorage.setItem('auth_token', result.data.token);
        if (result.data?.refreshToken) {
          localStorage.setItem('refresh_token', result.data.refreshToken);
        }
      }

      return { 
        data: result.data, 
        error: null, 
        success: true 
      };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Sign in failed', 
        success: false 
      };
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<ServiceResponse<null>> {
    try {
      // Call backend logout endpoint
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      // Clear local storage regardless of backend response
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');

      return { 
        data: null, 
        error: null, 
        success: true 
      };
    } catch (error) {
      // Still clear local storage even if backend call fails
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      
      return { 
        data: null, 
        error: null, 
        success: true 
      };
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<ServiceResponse<ApiUser>> {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        return { 
          data: null, 
          error: 'No authentication token found', 
          success: false 
        };
      }

      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: this.getAuthHeaders()
      });

      const result = await response.json();

      if (!response.ok) {
        // If token is invalid, clear it
        if (response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('refresh_token');
        }
        return { 
          data: null, 
          error: result.message || 'Failed to get current user', 
          success: false 
        };
      }

      return { 
        data: result.data.user, 
        error: null, 
        success: true 
      };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Failed to get current user', 
        success: false 
      };
    }
  }

  /**
   * Reset password
   */
  async resetPassword(data: ResetPasswordData): Promise<ServiceResponse<null>> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: data.email
        })
      });

      const result = await response.json();

      if (!response.ok) {
        return { 
          data: null, 
          error: result.message || 'Password reset failed', 
          success: false 
        };
      }

      return { 
        data: null, 
        error: null, 
        success: true 
      };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Password reset failed', 
        success: false 
      };
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<ServiceResponse<{ token: string; refreshToken: string }>> {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        return { 
          data: null, 
          error: 'No refresh token found', 
          success: false 
        };
      }

      const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          refreshToken
        })
      });

      const result = await response.json();

      if (!response.ok) {
        // Clear tokens if refresh fails
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        return { 
          data: null, 
          error: result.message || 'Token refresh failed', 
          success: false 
        };
      }

      // Store new tokens
      localStorage.setItem('auth_token', result.data.token);
      localStorage.setItem('refresh_token', result.data.refreshToken);

      return { 
        data: result.data, 
        error: null, 
        success: true 
      };
    } catch (error) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Token refresh failed', 
        success: false 
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }

  /**
   * Get stored auth token
   */
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }
}

export const apiAuthService = new ApiAuthService();
