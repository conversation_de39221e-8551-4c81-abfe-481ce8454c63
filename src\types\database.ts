
export interface Company {
  id: string;
  name: string;
  description: string | null;
  website: string | null;
  logo_url: string | null;
  location: string | null;
  created_at: string;
  updated_at: string;
}

export interface Job {
  id: string;
  company_id: string | null;
  title: string;
  description: string;
  location: string;
  salary_min: number | null;
  salary_max: number | null;
  job_type: string;
  category: string;
  is_remote: boolean | null;
  tags: string[] | null;
  requirements: string | null;
  benefits: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  company?: Company;
}

export interface JobApplication {
  id: string;
  job_id: string;
  user_id: string;
  applicant_name: string;
  applicant_email: string;
  resume_url: string | null;
  cover_letter: string | null;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface Profile {
  id: string;
  full_name: string | null;
  email: string | null;
  phone: string | null;
  location: string | null;
  bio: string | null;
  skills: string[] | null;
  experience_years: number | null;
  resume_url: string | null;
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
}
