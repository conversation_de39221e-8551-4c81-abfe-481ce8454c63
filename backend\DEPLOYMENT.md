# 🚀 Deployment Guide

This guide covers deploying the Career Link Spark backend API to various platforms without Docker.

## 📋 Prerequisites

- Node.js 16+ installed on the server
- Database (PostgreSQL, MySQL, or MongoDB)
- Domain name (optional)
- SSL certificate (for HTTPS)

## 🛠️ General Deployment Steps

### 1. **Server Setup**

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2
```

### 2. **Application Setup**

```bash
# Clone your repository
git clone <your-repository-url>
cd backend

# Install dependencies
npm ci --only=production

# Copy environment file
cp .env.example .env
# Edit .env with production values
nano .env
```

### 3. **Environment Configuration**

```env
# Production environment
NODE_ENV=production
PORT=5000

# Database (choose one)
DB_TYPE=postgres
DATABASE_URL=postgresql://username:password@localhost:5432/career_link_spark

# Security
JWT_SECRET=your-super-secure-production-jwt-secret
JWT_EXPIRES_IN=7d

# Email service
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# File upload
CLOUDINARY_CLOUD_NAME=your-production-cloud-name
CLOUDINARY_API_KEY=your-production-api-key
CLOUDINARY_API_SECRET=your-production-api-secret

# Frontend URL
FRONTEND_URL=https://yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 4. **Database Setup**

```bash
# Run migrations
npm run migrate

# Optional: Add sample data
npm run seed
```

### 5. **Start with PM2**

```bash
# Start the application
pm2 start server.js --name "career-link-api"

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
# Follow the instructions provided by the command above
```

## 🌐 Platform-Specific Deployments

### **Heroku**

1. **Install Heroku CLI**
   ```bash
   # Install Heroku CLI
   curl https://cli-assets.heroku.com/install.sh | sh
   ```

2. **Create Heroku App**
   ```bash
   heroku create your-app-name
   heroku addons:create heroku-postgresql:hobby-dev
   ```

3. **Set Environment Variables**
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set JWT_SECRET=your-jwt-secret
   heroku config:set EMAIL_HOST=smtp.gmail.com
   heroku config:set EMAIL_USER=<EMAIL>
   heroku config:set EMAIL_PASS=your-app-password
   heroku config:set CLOUDINARY_CLOUD_NAME=your-cloud-name
   heroku config:set CLOUDINARY_API_KEY=your-api-key
   heroku config:set CLOUDINARY_API_SECRET=your-api-secret
   heroku config:set FRONTEND_URL=https://your-frontend-domain.com
   ```

4. **Deploy**
   ```bash
   git push heroku main
   heroku run npm run migrate
   ```

### **DigitalOcean Droplet**

1. **Create Droplet**
   - Choose Ubuntu 20.04 LTS
   - Select appropriate size (minimum 1GB RAM)
   - Add SSH key

2. **Setup Server**
   ```bash
   # Connect to droplet
   ssh root@your-droplet-ip

   # Follow general deployment steps above
   ```

3. **Setup Nginx (Optional)**
   ```bash
   sudo apt install nginx

   # Create Nginx configuration
   sudo nano /etc/nginx/sites-available/career-link-api
   ```

   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

   ```bash
   # Enable site
   sudo ln -s /etc/nginx/sites-available/career-link-api /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### **AWS EC2**

1. **Launch EC2 Instance**
   - Choose Amazon Linux 2 or Ubuntu
   - Configure security groups (ports 22, 80, 443, 5000)

2. **Setup Application**
   ```bash
   # Connect to instance
   ssh -i your-key.pem ec2-user@your-instance-ip

   # Install Node.js
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   source ~/.bashrc
   nvm install 18
   nvm use 18

   # Follow general deployment steps
   ```

3. **Setup RDS Database** (Optional)
   - Create RDS PostgreSQL instance
   - Update DATABASE_URL in .env

### **Railway**

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Deploy**
   ```bash
   railway login
   railway init
   railway add postgresql
   railway deploy
   ```

3. **Set Environment Variables**
   ```bash
   railway variables set NODE_ENV=production
   railway variables set JWT_SECRET=your-jwt-secret
   # ... other variables
   ```

## 🔒 Security Considerations

### **Firewall Setup**
```bash
# Ubuntu/Debian
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### **SSL Certificate (Let's Encrypt)**
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### **Environment Security**
- Never commit .env files
- Use strong, unique JWT secrets
- Regularly rotate API keys
- Use environment-specific configurations

## 📊 Monitoring & Maintenance

### **PM2 Monitoring**
```bash
# View logs
pm2 logs career-link-api

# Monitor processes
pm2 monit

# Restart application
pm2 restart career-link-api

# View process list
pm2 list
```

### **Database Backups**
```bash
# PostgreSQL backup
pg_dump -h localhost -U username -d career_link_spark > backup.sql

# Restore
psql -h localhost -U username -d career_link_spark < backup.sql
```

### **Log Management**
```bash
# Setup log rotation
sudo nano /etc/logrotate.d/career-link-api
```

```
/home/<USER>/.pm2/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    notifempty
    create 0644 user user
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🚨 Troubleshooting

### **Common Issues**

1. **Port Already in Use**
   ```bash
   sudo lsof -i :5000
   sudo kill -9 <PID>
   ```

2. **Database Connection Issues**
   - Check DATABASE_URL format
   - Verify database server is running
   - Check firewall rules

3. **Permission Issues**
   ```bash
   sudo chown -R $USER:$USER /path/to/app
   chmod +x server.js
   ```

4. **Memory Issues**
   ```bash
   # Increase swap space
   sudo fallocate -l 1G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### **Health Check**
```bash
# Test API health
curl http://localhost:5000/health

# Test specific endpoint
curl http://localhost:5000/api/jobs
```

## 📈 Performance Optimization

### **PM2 Cluster Mode**
```bash
# Start in cluster mode
pm2 start server.js -i max --name "career-link-api"
```

### **Database Optimization**
- Enable connection pooling
- Add database indexes
- Regular VACUUM (PostgreSQL)

### **Caching**
- Implement Redis for session storage
- Add response caching for static data

## 🔄 Updates & Maintenance

### **Deployment Script**
```bash
#!/bin/bash
# deploy.sh

echo "Starting deployment..."

# Pull latest changes
git pull origin main

# Install dependencies
npm ci --only=production

# Run migrations
npm run migrate

# Restart application
pm2 restart career-link-api

echo "Deployment completed!"
```

```bash
chmod +x deploy.sh
./deploy.sh
```

Your backend API is now ready for production! 🚀
