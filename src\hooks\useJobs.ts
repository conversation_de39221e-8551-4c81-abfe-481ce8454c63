
import { useQuery } from "@tanstack/react-query";
import { jobService } from "@/services/jobService";
import type { JobFilters } from "@/services/types";

export const useJobs = (filters?: JobFilters, page: number = 1, pageSize: number = 10) => {
  return useQuery({
    queryKey: ['jobs', filters, page, pageSize],
    queryFn: async () => {
      console.log('Fetching jobs with filters:', filters);

      const result = await jobService.getJobs(filters, page, pageSize);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch jobs');
      }

      console.log('Jobs fetched successfully:', result.data?.data.length);
      return result.data;
    },
  });
};

export const useJob = (id: string) => {
  return useQuery({
    queryKey: ['job', id],
    queryFn: async () => {
      console.log('Fetching job:', id);

      const result = await jobService.getJobById(id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch job');
      }

      return result.data;
    },
    enabled: !!id,
  });
};

export const useFeaturedJobs = () => {
  return useQuery({
    queryKey: ['featured-jobs'],
    queryFn: async () => {
      console.log('Fetching featured jobs...');

      const result = await jobService.getFeaturedJobs();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch featured jobs');
      }

      console.log('Featured jobs fetched successfully:', result.data?.length);
      return result.data || [];
    },
  });
};
