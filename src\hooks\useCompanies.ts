import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { companyService } from "@/services/companyService";
import { useToast } from "@/hooks/use-toast";
import type { CreateCompanyRequest, UpdateCompanyRequest } from "@/services/types";

export const useCompanies = (page: number = 1, pageSize: number = 10, search?: string) => {
  return useQuery({
    queryKey: ['companies', page, pageSize, search],
    queryFn: async () => {
      const result = await companyService.getCompanies(page, pageSize, search);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch companies');
      }

      return result.data;
    },
  });
};

export const useCompany = (id: string) => {
  return useQuery({
    queryKey: ['company', id],
    queryFn: async () => {
      const result = await companyService.getCompanyById(id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch company');
      }

      return result.data;
    },
    enabled: !!id,
  });
};

export const useCreateCompany = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (companyData: CreateCompanyRequest) => {
      const result = await companyService.createCompany(companyData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create company');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Company created!",
        description: "The company has been successfully created.",
      });
      queryClient.invalidateQueries({ queryKey: ['companies'] });
    },
    onError: (error: any) => {
      toast({
        title: "Creation failed",
        description: error.message || "Failed to create company. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateCompany = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (companyData: UpdateCompanyRequest) => {
      const result = await companyService.updateCompany(companyData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update company');
      }

      return result.data;
    },
    onSuccess: (data) => {
      toast({
        title: "Company updated!",
        description: "The company has been successfully updated.",
      });
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      queryClient.invalidateQueries({ queryKey: ['company', data?.id] });
    },
    onError: (error: any) => {
      toast({
        title: "Update failed",
        description: error.message || "Failed to update company. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteCompany = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const result = await companyService.deleteCompany(id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete company');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Company deleted!",
        description: "The company has been successfully deleted.",
      });
      queryClient.invalidateQueries({ queryKey: ['companies'] });
    },
    onError: (error: any) => {
      toast({
        title: "Deletion failed",
        description: error.message || "Failed to delete company. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useCompanyJobs = (companyId: string, page: number = 1, pageSize: number = 10) => {
  return useQuery({
    queryKey: ['company-jobs', companyId, page, pageSize],
    queryFn: async () => {
      const result = await companyService.getCompanyJobs(companyId, page, pageSize);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch company jobs');
      }

      return result.data;
    },
    enabled: !!companyId,
  });
};

export const useCompanyStats = (companyId: string) => {
  return useQuery({
    queryKey: ['company-stats', companyId],
    queryFn: async () => {
      const result = await companyService.getCompanyStats(companyId);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch company statistics');
      }

      return result.data;
    },
    enabled: !!companyId,
  });
};
