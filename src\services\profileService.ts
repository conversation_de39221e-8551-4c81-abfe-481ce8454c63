import { supabase } from '@/integrations/supabase/client';
import type { 
  ProfileRow, 
  ProfileInsert, 
  ProfileUpdate,
  UpdateProfileRequest,
  ServiceResponse,
  ServiceError 
} from './types';

class ProfileService {
  /**
   * Get user profile by user ID
   */
  async getProfile(userId: string): Promise<ServiceResponse<ProfileRow>> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: data || null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch profile',
        success: false,
      };
    }
  }

  /**
   * Create a new user profile
   */
  async createProfile(
    userId: string, 
    profileData: Partial<UpdateProfileRequest>
  ): Promise<ServiceResponse<ProfileRow>> {
    try {
      const profileInsert: ProfileInsert = {
        id: userId,
        full_name: profileData.fullName || null,
        email: profileData.email || null,
        phone: profileData.phone || null,
        location: profileData.location || null,
        bio: profileData.bio || null,
        skills: profileData.skills || null,
        experience_years: profileData.experienceYears || null,
        resume_url: profileData.resumeUrl || null,
        avatar_url: profileData.avatarUrl || null,
      };

      const { data, error } = await supabase
        .from('profiles')
        .insert(profileInsert)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create profile',
        success: false,
      };
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(
    userId: string, 
    profileData: UpdateProfileRequest
  ): Promise<ServiceResponse<ProfileRow>> {
    try {
      // Validate profile data
      const validation = this.validateProfileData(profileData);
      if (!validation.isValid) {
        throw new ServiceError(
          `Validation failed: ${validation.errors.map(e => e.message).join(', ')}`
        );
      }

      const profileUpdate: ProfileUpdate = {
        ...(profileData.fullName !== undefined && { full_name: profileData.fullName }),
        ...(profileData.email !== undefined && { email: profileData.email }),
        ...(profileData.phone !== undefined && { phone: profileData.phone }),
        ...(profileData.location !== undefined && { location: profileData.location }),
        ...(profileData.bio !== undefined && { bio: profileData.bio }),
        ...(profileData.skills !== undefined && { skills: profileData.skills }),
        ...(profileData.experienceYears !== undefined && { experience_years: profileData.experienceYears }),
        ...(profileData.resumeUrl !== undefined && { resume_url: profileData.resumeUrl }),
        ...(profileData.avatarUrl !== undefined && { avatar_url: profileData.avatarUrl }),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('profiles')
        .update(profileUpdate)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to update profile',
        success: false,
      };
    }
  }

  /**
   * Delete user profile
   */
  async deleteProfile(userId: string): Promise<ServiceResponse<null>> {
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to delete profile',
        success: false,
      };
    }
  }

  /**
   * Upload profile avatar
   */
  async uploadAvatar(userId: string, file: File): Promise<ServiceResponse<string>> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Math.random()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('profiles')
        .upload(filePath, file);

      if (uploadError) {
        throw new ServiceError(uploadError.message);
      }

      const { data: { publicUrl } } = supabase.storage
        .from('profiles')
        .getPublicUrl(filePath);

      // Update profile with new avatar URL
      await this.updateProfile(userId, { avatarUrl: publicUrl });

      return {
        data: publicUrl,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to upload avatar',
        success: false,
      };
    }
  }

  /**
   * Upload resume
   */
  async uploadResume(userId: string, file: File): Promise<ServiceResponse<string>> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-resume-${Math.random()}.${fileExt}`;
      const filePath = `resumes/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('profiles')
        .upload(filePath, file);

      if (uploadError) {
        throw new ServiceError(uploadError.message);
      }

      const { data: { publicUrl } } = supabase.storage
        .from('profiles')
        .getPublicUrl(filePath);

      // Update profile with new resume URL
      await this.updateProfile(userId, { resumeUrl: publicUrl });

      return {
        data: publicUrl,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to upload resume',
        success: false,
      };
    }
  }

  /**
   * Search profiles by skills or location
   */
  async searchProfiles(
    query: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{
    data: ProfileRow[];
    count: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }>> {
    try {
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;

      let dbQuery = supabase
        .from('profiles')
        .select('*', { count: 'exact' })
        .order('updated_at', { ascending: false })
        .range(from, to);

      if (query) {
        dbQuery = dbQuery.or(`
          full_name.ilike.%${query}%,
          bio.ilike.%${query}%,
          location.ilike.%${query}%,
          skills.cs.{${query}}
        `);
      }

      const { data, error, count } = await dbQuery;

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const totalPages = count ? Math.ceil(count / pageSize) : 0;

      return {
        data: {
          data: data || [],
          count: count || 0,
          page,
          pageSize,
          totalPages,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to search profiles',
        success: false,
      };
    }
  }

  /**
   * Get profile completion percentage
   */
  getProfileCompletion(profile: ProfileRow): number {
    const fields = [
      'full_name',
      'email',
      'phone',
      'location',
      'bio',
      'skills',
      'experience_years',
      'resume_url',
      'avatar_url'
    ];

    const completedFields = fields.filter(field => {
      const value = profile[field as keyof ProfileRow];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== null && value !== undefined && value !== '';
    });

    return Math.round((completedFields.length / fields.length) * 100);
  }

  /**
   * Validate profile data
   */
  private validateProfileData(profileData: UpdateProfileRequest) {
    const errors: Array<{ field: string; message: string }> = [];

    if (profileData.email && !this.isValidEmail(profileData.email)) {
      errors.push({ field: 'email', message: 'Please enter a valid email address' });
    }

    if (profileData.phone && !this.isValidPhone(profileData.phone)) {
      errors.push({ field: 'phone', message: 'Please enter a valid phone number' });
    }

    if (profileData.experienceYears !== undefined && profileData.experienceYears < 0) {
      errors.push({ field: 'experienceYears', message: 'Experience years cannot be negative' });
    }

    if (profileData.resumeUrl && !this.isValidUrl(profileData.resumeUrl)) {
      errors.push({ field: 'resumeUrl', message: 'Please enter a valid resume URL' });
    }

    if (profileData.avatarUrl && !this.isValidUrl(profileData.avatarUrl)) {
      errors.push({ field: 'avatarUrl', message: 'Please enter a valid avatar URL' });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

export const profileService = new ProfileService();
