const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { executeQuery, getCollection, connectDatabase } = require('../config/database');
require('dotenv').config();

// Sample data
const sampleData = {
  users: [
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: 'Admin123!',
      full_name: 'Admin User',
      role: 'admin',
      is_verified: true
    },
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: 'Employer123!',
      full_name: '<PERSON>loy<PERSON>',
      role: 'employer',
      is_verified: true
    },
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: 'User123!',
      full_name: '<PERSON>',
      role: 'user',
      is_verified: true
    }
  ],

  companies: [
    {
      id: uuidv4(),
      name: 'TechCorp Solutions',
      description: 'Leading technology solutions provider specializing in cloud computing and AI.',
      website: 'https://techcorp.com',
      location: 'San Francisco, CA'
    },
    {
      id: uuidv4(),
      name: 'InnovateLabs',
      description: 'Cutting-edge research and development company focused on emerging technologies.',
      website: 'https://innovatelabs.com',
      location: 'Austin, TX'
    },
    {
      id: uuidv4(),
      name: 'DataDriven Inc',
      description: 'Data analytics and business intelligence solutions for enterprises.',
      website: 'https://datadriven.com',
      location: 'New York, NY'
    }
  ],

  jobs: [
    {
      id: uuidv4(),
      title: 'Senior Full Stack Developer',
      description: 'We are looking for an experienced Full Stack Developer to join our dynamic team. You will be responsible for developing and maintaining web applications using modern technologies.',
      location: 'San Francisco, CA',
      salary_min: 120000,
      salary_max: 180000,
      job_type: 'Full-time',
      category: 'Engineering',
      is_remote: true,
      requirements: 'Bachelor\'s degree in Computer Science or related field\n5+ years of experience in full stack development\nProficiency in React, Node.js, and PostgreSQL\nExperience with cloud platforms (AWS, Azure, or GCP)',
      benefits: 'Competitive salary and equity\nHealth, dental, and vision insurance\nFlexible work arrangements\nProfessional development budget',
      tags: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Remote']
    },
    {
      id: uuidv4(),
      title: 'Product Manager',
      description: 'Join our product team to drive the development of innovative solutions. You will work closely with engineering, design, and business teams to deliver exceptional products.',
      location: 'Austin, TX',
      salary_min: 100000,
      salary_max: 150000,
      job_type: 'Full-time',
      category: 'Product',
      is_remote: false,
      requirements: 'MBA or equivalent experience\n3+ years of product management experience\nStrong analytical and communication skills\nExperience with agile development methodologies',
      benefits: 'Stock options\nHealth insurance\nFlexible PTO\nTeam building events',
      tags: ['Product Management', 'Agile', 'Strategy', 'Analytics']
    },
    {
      id: uuidv4(),
      title: 'Data Scientist',
      description: 'We are seeking a talented Data Scientist to help us extract insights from large datasets and build predictive models to drive business decisions.',
      location: 'New York, NY',
      salary_min: 110000,
      salary_max: 160000,
      job_type: 'Full-time',
      category: 'Engineering',
      is_remote: true,
      requirements: 'Master\'s degree in Data Science, Statistics, or related field\n3+ years of experience in data science\nProficiency in Python, R, and SQL\nExperience with machine learning frameworks',
      benefits: 'Competitive compensation\nComprehensive health benefits\nRemote work options\nLearning and development opportunities',
      tags: ['Python', 'Machine Learning', 'SQL', 'Statistics', 'Remote']
    },
    {
      id: uuidv4(),
      title: 'Frontend Developer',
      description: 'Looking for a creative Frontend Developer to build beautiful and responsive user interfaces. You will work with our design team to bring mockups to life.',
      location: 'Remote',
      salary_min: 80000,
      salary_max: 120000,
      job_type: 'Full-time',
      category: 'Engineering',
      is_remote: true,
      requirements: 'Bachelor\'s degree or equivalent experience\n2+ years of frontend development experience\nProficiency in HTML, CSS, JavaScript, and React\nExperience with responsive design',
      benefits: 'Fully remote position\nFlexible hours\nHealth insurance\nProfessional development budget',
      tags: ['React', 'JavaScript', 'CSS', 'HTML', 'Remote', 'UI/UX']
    },
    {
      id: uuidv4(),
      title: 'DevOps Engineer',
      description: 'Join our infrastructure team to build and maintain scalable, reliable systems. You will work on automation, monitoring, and deployment pipelines.',
      location: 'Seattle, WA',
      salary_min: 130000,
      salary_max: 190000,
      job_type: 'Full-time',
      category: 'Engineering',
      is_remote: false,
      requirements: 'Bachelor\'s degree in Computer Science or related field\n4+ years of DevOps experience\nExperience with Docker, Kubernetes, and CI/CD\nKnowledge of cloud platforms and infrastructure as code',
      benefits: 'Competitive salary\nStock options\nHealth benefits\nRelocation assistance',
      tags: ['DevOps', 'Kubernetes', 'Docker', 'AWS', 'CI/CD']
    }
  ]
};

// Seed functions
const seedUsers = async () => {
  const dbType = process.env.DB_TYPE || 'postgres';
  const saltRounds = 12;

  console.log('Seeding users...');

  for (const user of sampleData.users) {
    try {
      // Hash password
      const hashedPassword = await bcrypt.hash(user.password, saltRounds);
      
      const userData = {
        ...user,
        password: hashedPassword,
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const usersCollection = await getCollection('users');
        await usersCollection.insertOne({ _id: user.id, ...userData });
      } else {
        const insertQuery = dbType === 'mysql'
          ? 'INSERT INTO users (id, email, password, full_name, role, is_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
          : 'INSERT INTO users (id, email, password, full_name, role, is_verified, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)';
        
        await executeQuery(insertQuery, [
          user.id, user.email, hashedPassword, user.full_name,
          user.role, user.is_verified, new Date(), new Date()
        ]);
      }

      console.log(`✅ User created: ${user.email}`);
    } catch (error) {
      if (error.message.includes('duplicate') || error.message.includes('unique')) {
        console.log(`⚠️  User already exists: ${user.email}`);
      } else {
        console.error(`❌ Error creating user ${user.email}:`, error.message);
      }
    }
  }
};

const seedCompanies = async () => {
  const dbType = process.env.DB_TYPE || 'postgres';

  console.log('Seeding companies...');

  for (const company of sampleData.companies) {
    try {
      const companyData = {
        ...company,
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        await companiesCollection.insertOne({ _id: company.id, ...companyData });
      } else {
        const insertQuery = dbType === 'mysql'
          ? 'INSERT INTO companies (id, name, description, website, location, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)'
          : 'INSERT INTO companies (id, name, description, website, location, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7)';
        
        await executeQuery(insertQuery, [
          company.id, company.name, company.description, company.website,
          company.location, new Date(), new Date()
        ]);
      }

      console.log(`✅ Company created: ${company.name}`);
    } catch (error) {
      if (error.message.includes('duplicate') || error.message.includes('unique')) {
        console.log(`⚠️  Company already exists: ${company.name}`);
      } else {
        console.error(`❌ Error creating company ${company.name}:`, error.message);
      }
    }
  }
};

const seedJobs = async () => {
  const dbType = process.env.DB_TYPE || 'postgres';

  console.log('Seeding jobs...');

  // Get employer user ID
  const employerUser = sampleData.users.find(u => u.role === 'employer');
  
  for (let i = 0; i < sampleData.jobs.length; i++) {
    const job = sampleData.jobs[i];
    const company = sampleData.companies[i % sampleData.companies.length];
    
    try {
      const jobData = {
        ...job,
        company_id: company.id,
        posted_by: employerUser.id,
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        await jobsCollection.insertOne({ _id: job.id, ...jobData });
      } else {
        const tagsJson = dbType === 'mysql' ? JSON.stringify(job.tags) : job.tags;
        
        const insertQuery = dbType === 'mysql'
          ? 'INSERT INTO jobs (id, title, description, location, salary_min, salary_max, job_type, category, is_remote, requirements, benefits, tags, company_id, posted_by, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)'
          : 'INSERT INTO jobs (id, title, description, location, salary_min, salary_max, job_type, category, is_remote, requirements, benefits, tags, company_id, posted_by, status, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)';
        
        await executeQuery(insertQuery, [
          job.id, job.title, job.description, job.location,
          job.salary_min, job.salary_max, job.job_type, job.category,
          job.is_remote, job.requirements, job.benefits, tagsJson,
          company.id, employerUser.id, 'active', new Date(), new Date()
        ]);
      }

      console.log(`✅ Job created: ${job.title}`);
    } catch (error) {
      console.error(`❌ Error creating job ${job.title}:`, error.message);
    }
  }
};

const seedProfiles = async () => {
  const dbType = process.env.DB_TYPE || 'postgres';

  console.log('Seeding profiles...');

  for (const user of sampleData.users) {
    try {
      const profileData = {
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        await profilesCollection.insertOne({ _id: user.id, ...profileData });
      } else {
        const insertQuery = dbType === 'mysql'
          ? 'INSERT INTO profiles (id, full_name, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?)'
          : 'INSERT INTO profiles (id, full_name, email, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)';
        
        await executeQuery(insertQuery, [
          user.id, user.full_name, user.email, new Date(), new Date()
        ]);
      }

      console.log(`✅ Profile created: ${user.full_name}`);
    } catch (error) {
      if (error.message.includes('duplicate') || error.message.includes('unique')) {
        console.log(`⚠️  Profile already exists: ${user.full_name}`);
      } else {
        console.error(`❌ Error creating profile ${user.full_name}:`, error.message);
      }
    }
  }
};

// Main seed function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    await connectDatabase();

    await seedUsers();
    await seedCompanies();
    await seedJobs();
    await seedProfiles();

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Sample accounts created:');
    console.log('Admin: <EMAIL> / Admin123!');
    console.log('Employer: <EMAIL> / Employer123!');
    console.log('User: <EMAIL> / User123!');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding script failed:', error);
      process.exit(1);
    });
}

module.exports = { seedDatabase };
