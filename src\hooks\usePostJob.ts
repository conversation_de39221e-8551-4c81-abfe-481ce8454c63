import { useMutation, useQueryClient } from "@tanstack/react-query";
import { jobService } from "@/services/jobService";
import { useToast } from "@/hooks/use-toast";
import type { CreateJobRequest, UpdateJobRequest } from "@/services/types";

export const useCreateJob = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (jobData: CreateJobRequest) => {
      const result = await jobService.createJob(jobData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create job');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Job posted successfully!",
        description: "Your job posting has been created and is now live.",
      });
      queryClient.invalidateQueries({ queryKey: ['jobs'] });
      queryClient.invalidateQueries({ queryKey: ['featured-jobs'] });
    },
    onError: (error: any) => {
      toast({
        title: "Job posting failed",
        description: error.message || "Failed to create job posting. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateJob = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (jobData: UpdateJobRequest) => {
      const result = await jobService.updateJob(jobData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update job');
      }

      return result.data;
    },
    onSuccess: (data) => {
      toast({
        title: "Job updated!",
        description: "Your job posting has been successfully updated.",
      });
      queryClient.invalidateQueries({ queryKey: ['jobs'] });
      queryClient.invalidateQueries({ queryKey: ['job', data?.id] });
      queryClient.invalidateQueries({ queryKey: ['featured-jobs'] });
    },
    onError: (error: any) => {
      toast({
        title: "Update failed",
        description: error.message || "Failed to update job posting. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteJob = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const result = await jobService.deleteJob(id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete job');
      }

      return result.data;
    },
    onSuccess: () => {
      toast({
        title: "Job deleted!",
        description: "Your job posting has been successfully deleted.",
      });
      queryClient.invalidateQueries({ queryKey: ['jobs'] });
      queryClient.invalidateQueries({ queryKey: ['featured-jobs'] });
    },
    onError: (error: any) => {
      toast({
        title: "Deletion failed",
        description: error.message || "Failed to delete job posting. Please try again.",
        variant: "destructive",
      });
    },
  });
};
