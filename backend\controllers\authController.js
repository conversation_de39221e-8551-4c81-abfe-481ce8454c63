const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { executeQuery, getCollection } = require('../config/database');
const { generateToken, generateRefreshToken, verifyRefreshToken } = require('../middleware/auth');
const { sendEmail } = require('../utils/emailService');

class AuthController {
  // Register new user
  async register(req, res) {
    try {
      const { email, password, fullName, role = 'user' } = req.body;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if user already exists
      let existingUser;
      if (dbType === 'mongodb') {
        const usersCollection = await getCollection('users');
        existingUser = await usersCollection.findOne({ email });
      } else {
        const query = dbType === 'mysql' 
          ? 'SELECT * FROM users WHERE email = ?'
          : 'SELECT * FROM users WHERE email = $1';
        const users = await executeQuery(query, [email]);
        existingUser = users[0];
      }

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email already exists'
        });
      }

      // Hash password
      const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      const userId = uuidv4();
      const userData = {
        id: userId,
        email,
        password: hashedPassword,
        full_name: fullName,
        role,
        is_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      };

      let newUser;
      if (dbType === 'mongodb') {
        const usersCollection = await getCollection('users');
        await usersCollection.insertOne({ _id: userId, ...userData });
        newUser = { id: userId, ...userData };
      } else {
        const insertQuery = dbType === 'mysql'
          ? 'INSERT INTO users (id, email, password, full_name, role, is_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
          : 'INSERT INTO users (id, email, password, full_name, role, is_verified, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *';
        
        if (dbType === 'mysql') {
          await executeQuery(insertQuery, [userId, email, hashedPassword, fullName, role, false, new Date(), new Date()]);
          newUser = { id: userId, email, full_name: fullName, role };
        } else {
          const result = await executeQuery(insertQuery, [userId, email, hashedPassword, fullName, role, false, new Date(), new Date()]);
          newUser = result[0];
        }
      }

      // Create profile
      await this.createUserProfile(userId, fullName, email);

      // Generate tokens
      const token = generateToken(userId, role);
      const refreshToken = generateRefreshToken(userId);

      // Send verification email (optional)
      try {
        await sendEmail({
          to: email,
          subject: 'Welcome to Career Link Spark',
          template: 'welcome',
          data: { fullName, verificationLink: `${process.env.FRONTEND_URL}/verify-email?token=${token}` }
        });
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
      }

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: {
            id: newUser.id,
            email: newUser.email,
            fullName: newUser.full_name,
            role: newUser.role
          },
          token,
          refreshToken
        }
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Login user
  async login(req, res) {
    try {
      const { email, password } = req.body;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Find user
      let user;
      if (dbType === 'mongodb') {
        const usersCollection = await getCollection('users');
        user = await usersCollection.findOne({ email });
      } else {
        const query = dbType === 'mysql' 
          ? 'SELECT * FROM users WHERE email = ?'
          : 'SELECT * FROM users WHERE email = $1';
        const users = await executeQuery(query, [email]);
        user = users[0];
      }

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Update last login
      const updateQuery = dbType === 'mysql'
        ? 'UPDATE users SET last_login = ? WHERE id = ?'
        : 'UPDATE users SET last_login = $1 WHERE id = $2';
      
      if (dbType === 'mongodb') {
        const usersCollection = await getCollection('users');
        await usersCollection.updateOne(
          { _id: user._id || user.id },
          { $set: { last_login: new Date() } }
        );
      } else {
        await executeQuery(updateQuery, [new Date(), user.id]);
      }

      // Generate tokens
      const token = generateToken(user.id, user.role);
      const refreshToken = generateRefreshToken(user.id);

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            email: user.email,
            fullName: user.full_name,
            role: user.role,
            isVerified: user.is_verified
          },
          token,
          refreshToken
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Refresh token
  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          message: 'Refresh token is required'
        });
      }

      const decoded = verifyRefreshToken(refreshToken);
      
      // Generate new tokens
      const newToken = generateToken(decoded.userId);
      const newRefreshToken = generateRefreshToken(decoded.userId);

      res.json({
        success: true,
        data: {
          token: newToken,
          refreshToken: newRefreshToken
        }
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }
  }

  // Get current user
  async getCurrentUser(req, res) {
    try {
      const user = req.user;
      
      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            fullName: user.full_name,
            role: user.role,
            isVerified: user.is_verified
          }
        }
      });
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Logout (optional - mainly for clearing refresh tokens)
  async logout(req, res) {
    try {
      // In a more complex setup, you might want to blacklist the token
      // or remove refresh tokens from database
      
      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Create user profile
  async createUserProfile(userId, fullName, email) {
    try {
      const dbType = process.env.DB_TYPE || 'postgres';
      const profileData = {
        id: userId,
        full_name: fullName,
        email,
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        await profilesCollection.insertOne({ _id: userId, ...profileData });
      } else {
        const insertQuery = dbType === 'mysql'
          ? 'INSERT INTO profiles (id, full_name, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?)'
          : 'INSERT INTO profiles (id, full_name, email, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)';
        
        await executeQuery(insertQuery, [userId, fullName, email, new Date(), new Date()]);
      }
    } catch (error) {
      console.error('Error creating user profile:', error);
      // Don't throw error as this is not critical for registration
    }
  }
}

module.exports = new AuthController();
