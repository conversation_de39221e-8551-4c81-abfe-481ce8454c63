# Career Link Spark Backend API

A comprehensive Node.js/Express backend API for the Career Link Spark job portal application with support for multiple databases (PostgreSQL, MySQL, MongoDB).

## 🚀 Features

- **Multi-Database Support**: PostgreSQL, MySQL, and MongoDB
- **Authentication & Authorization**: JWT-based auth with role-based access control
- **File Upload**: Cloudinary integration for avatars and resumes
- **Email Service**: Nodemailer integration for notifications
- **Input Validation**: Comprehensive request validation
- **Rate Limiting**: Protection against abuse
- **Security**: Helmet, CORS, and other security middleware
- **Error Handling**: Centralized error handling with detailed logging
- **API Documentation**: Built-in endpoint documentation

## 📋 Prerequisites

- Node.js (v16 or higher)
- Database (PostgreSQL, MySQL, or MongoDB)
- Cloudinary account (for file uploads)
- Email service (Gmail, SendGrid, etc.)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   # Server
   PORT=5000
   NODE_ENV=development
   
   # Database (choose one)
   DB_TYPE=postgres  # postgres, mysql, or mongodb
   DATABASE_URL=postgresql://username:password@localhost:5432/career_link_spark
   
   # JWT
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRES_IN=7d
   
   # Email
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   
   # Cloudinary
   CLOUDINARY_CLOUD_NAME=your-cloud-name
   CLOUDINARY_API_KEY=your-api-key
   CLOUDINARY_API_SECRET=your-api-secret
   
   # Frontend
   FRONTEND_URL=http://localhost:3000
   ```

4. **Database Setup**
   ```bash
   # Run migrations
   npm run migrate
   
   # Seed sample data (optional)
   npm run seed
   ```

5. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## 📚 API Documentation

### Base URL
```
http://localhost:5000/api
```

### Authentication Endpoints
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user
- `POST /auth/refresh-token` - Refresh access token
- `GET /auth/me` - Get current user
- `POST /auth/logout` - Logout user
- `POST /auth/forgot-password` - Send password reset email
- `POST /auth/reset-password` - Reset password with token

### Job Endpoints
- `GET /jobs` - Get all jobs with filtering
- `GET /jobs/featured` - Get featured jobs
- `GET /jobs/:id` - Get single job
- `POST /jobs` - Create new job (Employer)
- `PUT /jobs/:id` - Update job (Owner)
- `DELETE /jobs/:id` - Delete job (Owner)

### Application Endpoints
- `POST /applications` - Submit job application
- `GET /applications/my-applications` - Get user applications
- `GET /applications/job/:jobId` - Get job applications (Employer)
- `PUT /applications/:id/status` - Update application status

### Company Endpoints
- `GET /companies` - Get all companies
- `GET /companies/:id` - Get single company
- `POST /companies` - Create company (Employer)
- `PUT /companies/:id` - Update company (Admin)

### Profile Endpoints
- `GET /profiles/me` - Get current user profile
- `PUT /profiles/me` - Update profile
- `POST /profiles/upload-avatar` - Upload avatar
- `POST /profiles/upload-resume` - Upload resume

## 🗄️ Database Support

### PostgreSQL
```env
DB_TYPE=postgres
DATABASE_URL=postgresql://username:password@localhost:5432/career_link_spark
```

### MySQL
```env
DB_TYPE=mysql
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=career_link_spark
```

### MongoDB
```env
DB_TYPE=mongodb
MONGODB_URI=mongodb://localhost:27017/career_link_spark
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### User Roles
- **user**: Can apply for jobs, manage profile
- **employer**: Can post jobs, manage applications
- **admin**: Full access to all resources

## 📁 Project Structure

```
backend/
├── config/
│   └── database.js          # Database configuration
├── controllers/
│   ├── authController.js    # Authentication logic
│   ├── jobController.js     # Job management
│   ├── jobApplicationController.js
│   ├── companyController.js
│   └── profileController.js
├── middleware/
│   ├── auth.js              # Authentication middleware
│   └── validation.js        # Input validation
├── routes/
│   ├── auth.js              # Auth routes
│   ├── jobs.js              # Job routes
│   ├── applications.js      # Application routes
│   ├── companies.js         # Company routes
│   └── profiles.js          # Profile routes
├── scripts/
│   ├── migrate.js           # Database migrations
│   └── seed.js              # Sample data seeding
├── utils/
│   ├── emailService.js      # Email utilities
│   └── fileUpload.js        # File upload utilities
├── server.js                # Main server file
├── package.json
└── README.md
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 📊 API Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

### Paginated Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 10,
      "totalCount": 100,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

## 🔧 Configuration Options

### Rate Limiting
```env
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # 100 requests per window
```

### Security
```env
BCRYPT_SALT_ROUNDS=12        # Password hashing rounds
SESSION_SECRET=your-session-secret
```

### CORS
```env
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
```

## 🚀 Deployment

### Environment Variables
Ensure all required environment variables are set in production:

- `NODE_ENV=production`
- Database connection strings
- JWT secrets
- Email configuration
- Cloudinary credentials

### Docker Deployment
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

### Health Check
The API includes a health check endpoint:
```
GET /health
```

## 📝 Sample Data

After running the seed script, you can use these sample accounts:

- **Admin**: <EMAIL> / Admin123!
- **Employer**: <EMAIL> / Employer123!
- **User**: <EMAIL> / User123!

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/api/docs`
- Review the health check at `/health`
