import { supabase } from '@/integrations/supabase/client';
import type { User, Session, AuthError } from '@supabase/supabase-js';
import { ServiceResponse, ServiceError } from './types';

export interface SignUpData {
  email: string;
  password: string;
  fullName?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface ResetPasswordData {
  email: string;
}

export interface UpdatePasswordData {
  password: string;
  accessToken: string;
}

export interface AuthServiceResponse<T = any> extends ServiceResponse<T> {
  user?: User | null;
  session?: Session | null;
}

class AuthService {
  /**
   * Sign up a new user
   */
  async signUp(data: SignUpData): Promise<AuthServiceResponse> {
    try {
      const { email, password, fullName } = data;

      const { data: authData, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
          emailRedirectTo: `${window.location.origin}/`,
        },
      });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      // Create profile if user was created successfully
      if (authData.user && !error) {
        await this.createUserProfile(authData.user, fullName);
      }

      return {
        data: authData,
        user: authData.user,
        session: authData.session,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Sign in an existing user
   */
  async signIn(data: SignInData): Promise<AuthServiceResponse> {
    try {
      const { email, password } = data;

      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: authData,
        user: authData.user,
        session: authData.session,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<ServiceResponse<null>> {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get current user session
   */
  async getCurrentSession(): Promise<AuthServiceResponse> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: session,
        user: session?.user || null,
        session,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<AuthServiceResponse<User>> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: user,
        user,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Reset password
   */
  async resetPassword(data: ResetPasswordData): Promise<ServiceResponse<null>> {
    try {
      const { email } = data;

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Update password
   */
  async updatePassword(data: UpdatePasswordData): Promise<ServiceResponse<User>> {
    try {
      const { password } = data;

      const { data: userData, error } = await supabase.auth.updateUser({
        password,
      });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: userData.user,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Create user profile after successful signup
   */
  private async createUserProfile(user: User, fullName?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          full_name: fullName || null,
          email: user.email || null,
        });

      if (error) {
        console.error('Error creating user profile:', error);
      }
    } catch (error) {
      console.error('Error creating user profile:', error);
    }
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}

export const authService = new AuthService();
