import { supabase } from '@/integrations/supabase/client';
import type { 
  JobApplicationRow, 
  JobApplicationInsert, 
  JobApplicationUpdate,
  JobApplicationWithDetails,
  CreateJobApplicationRequest,
  UpdateJobApplicationRequest,
  ServiceResponse,
  PaginatedResponse,
  ServiceError 
} from './types';

class JobApplicationService {
  /**
   * Create a new job application
   */
  async createApplication(
    applicationData: CreateJobApplicationRequest,
    userId: string,
    userEmail: string,
    userName: string
  ): Promise<ServiceResponse<JobApplicationRow>> {
    try {
      // Check if user has already applied for this job
      const existingApplication = await this.getApplicationByJobAndUser(
        applicationData.jobId, 
        userId
      );

      if (existingApplication.success && existingApplication.data) {
        throw new ServiceError('You have already applied for this job');
      }

      const applicationInsert: JobApplicationInsert = {
        job_id: applicationData.jobId,
        user_id: userId,
        applicant_name: userName,
        applicant_email: userEmail,
        cover_letter: applicationData.coverLetter || null,
        resume_url: applicationData.resumeUrl || null,
        status: 'pending',
      };

      const { data, error } = await supabase
        .from('job_applications')
        .insert(applicationInsert)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create application',
        success: false,
      };
    }
  }

  /**
   * Get applications by user ID
   */
  async getApplicationsByUser(
    userId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<PaginatedResponse<JobApplicationWithDetails>>> {
    try {
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;

      const { data, error, count } = await supabase
        .from('job_applications')
        .select(`
          *,
          job:jobs(
            *,
            company:companies(*)
          )
        `, { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const totalPages = count ? Math.ceil(count / pageSize) : 0;

      return {
        data: {
          data: data || [],
          count: count || 0,
          page,
          pageSize,
          totalPages,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch applications',
        success: false,
      };
    }
  }

  /**
   * Get applications by job ID (for employers)
   */
  async getApplicationsByJob(
    jobId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<PaginatedResponse<JobApplicationWithDetails>>> {
    try {
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;

      const { data, error, count } = await supabase
        .from('job_applications')
        .select(`
          *,
          profile:profiles(*)
        `, { count: 'exact' })
        .eq('job_id', jobId)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const totalPages = count ? Math.ceil(count / pageSize) : 0;

      return {
        data: {
          data: data || [],
          count: count || 0,
          page,
          pageSize,
          totalPages,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch job applications',
        success: false,
      };
    }
  }

  /**
   * Get a single application by ID
   */
  async getApplicationById(id: string): Promise<ServiceResponse<JobApplicationWithDetails>> {
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .select(`
          *,
          job:jobs(
            *,
            company:companies(*)
          ),
          profile:profiles(*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch application',
        success: false,
      };
    }
  }

  /**
   * Update an application
   */
  async updateApplication(
    applicationData: UpdateJobApplicationRequest
  ): Promise<ServiceResponse<JobApplicationRow>> {
    try {
      const { id, ...updateData } = applicationData;

      const applicationUpdate: JobApplicationUpdate = {
        ...(updateData.status && { status: updateData.status }),
        ...(updateData.coverLetter !== undefined && { cover_letter: updateData.coverLetter }),
        ...(updateData.resumeUrl !== undefined && { resume_url: updateData.resumeUrl }),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('job_applications')
        .update(applicationUpdate)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to update application',
        success: false,
      };
    }
  }

  /**
   * Delete an application
   */
  async deleteApplication(id: string): Promise<ServiceResponse<null>> {
    try {
      const { error } = await supabase
        .from('job_applications')
        .delete()
        .eq('id', id);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to delete application',
        success: false,
      };
    }
  }

  /**
   * Check if user has applied for a specific job
   */
  async getApplicationByJobAndUser(
    jobId: string, 
    userId: string
  ): Promise<ServiceResponse<JobApplicationRow>> {
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .select('*')
        .eq('job_id', jobId)
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
        throw new ServiceError(error.message, error.code);
      }

      return {
        data: data || null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to check application status',
        success: false,
      };
    }
  }

  /**
   * Get application statistics for a user
   */
  async getUserApplicationStats(userId: string): Promise<ServiceResponse<{
    total: number;
    pending: number;
    reviewed: number;
    accepted: number;
    rejected: number;
  }>> {
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .select('status')
        .eq('user_id', userId);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const stats = {
        total: data?.length || 0,
        pending: data?.filter(app => app.status === 'pending').length || 0,
        reviewed: data?.filter(app => app.status === 'reviewed').length || 0,
        accepted: data?.filter(app => app.status === 'accepted').length || 0,
        rejected: data?.filter(app => app.status === 'rejected').length || 0,
      };

      return {
        data: stats,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch application statistics',
        success: false,
      };
    }
  }

  /**
   * Get application statistics for a job (for employers)
   */
  async getJobApplicationStats(jobId: string): Promise<ServiceResponse<{
    total: number;
    pending: number;
    reviewed: number;
    accepted: number;
    rejected: number;
  }>> {
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .select('status')
        .eq('job_id', jobId);

      if (error) {
        throw new ServiceError(error.message, error.code);
      }

      const stats = {
        total: data?.length || 0,
        pending: data?.filter(app => app.status === 'pending').length || 0,
        reviewed: data?.filter(app => app.status === 'reviewed').length || 0,
        accepted: data?.filter(app => app.status === 'accepted').length || 0,
        rejected: data?.filter(app => app.status === 'rejected').length || 0,
      };

      return {
        data: stats,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch job application statistics',
        success: false,
      };
    }
  }
}

export const jobApplicationService = new JobApplicationService();
