const { v4: uuidv4 } = require('uuid');
const { executeQuery, getCollection } = require('../config/database');

class JobController {
  // Get all jobs with filtering and pagination
  async getJobs(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        location,
        jobType,
        category,
        isRemote,
        salaryMin,
        salaryMax,
        tags
      } = req.query;

      const offset = (page - 1) * limit;
      const dbType = process.env.DB_TYPE || 'postgres';

      let jobs, totalCount;

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        const companiesCollection = await getCollection('companies');

        // Build filter
        const filter = { status: 'active' };
        
        if (search) {
          filter.$or = [
            { title: { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } }
          ];
        }
        if (location) filter.location = { $regex: location, $options: 'i' };
        if (jobType) filter.job_type = jobType;
        if (category) filter.category = category;
        if (isRemote !== undefined) filter.is_remote = isRemote === 'true';
        if (salaryMin) filter.salary_min = { $gte: parseInt(salaryMin) };
        if (salaryMax) filter.salary_max = { $lte: parseInt(salaryMax) };
        if (tags) filter.tags = { $in: Array.isArray(tags) ? tags : [tags] };

        // Get jobs with pagination
        jobs = await jobsCollection
          .find(filter)
          .sort({ created_at: -1 })
          .skip(offset)
          .limit(parseInt(limit))
          .toArray();

        // Get company details for each job
        for (let job of jobs) {
          if (job.company_id) {
            job.company = await companiesCollection.findOne({ _id: job.company_id });
          }
        }

        totalCount = await jobsCollection.countDocuments(filter);
      } else {
        // SQL query building
        let whereConditions = ['j.status = ?'];
        let queryParams = ['active'];
        let paramIndex = 2;

        if (search) {
          whereConditions.push(`(j.title ILIKE ? OR j.description ILIKE ?)`);
          queryParams.push(`%${search}%`, `%${search}%`);
          paramIndex += 2;
        }
        if (location) {
          whereConditions.push('j.location ILIKE ?');
          queryParams.push(`%${location}%`);
          paramIndex++;
        }
        if (jobType) {
          whereConditions.push('j.job_type = ?');
          queryParams.push(jobType);
          paramIndex++;
        }
        if (category) {
          whereConditions.push('j.category = ?');
          queryParams.push(category);
          paramIndex++;
        }
        if (isRemote !== undefined) {
          whereConditions.push('j.is_remote = ?');
          queryParams.push(isRemote === 'true');
          paramIndex++;
        }
        if (salaryMin) {
          whereConditions.push('j.salary_min >= ?');
          queryParams.push(parseInt(salaryMin));
          paramIndex++;
        }
        if (salaryMax) {
          whereConditions.push('j.salary_max <= ?');
          queryParams.push(parseInt(salaryMax));
          paramIndex++;
        }

        const whereClause = whereConditions.join(' AND ');

        // Get jobs with company details
        const jobsQuery = `
          SELECT j.*, c.name as company_name, c.description as company_description,
                 c.website as company_website, c.logo_url as company_logo_url,
                 c.location as company_location
          FROM jobs j
          LEFT JOIN companies c ON j.company_id = c.id
          WHERE ${whereClause}
          ORDER BY j.created_at DESC
          LIMIT ? OFFSET ?
        `;

        queryParams.push(parseInt(limit), offset);
        jobs = await executeQuery(jobsQuery, queryParams);

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM jobs j
          WHERE ${whereClause}
        `;
        const countParams = queryParams.slice(0, -2); // Remove limit and offset
        const countResult = await executeQuery(countQuery, countParams);
        totalCount = countResult[0].total;

        // Format jobs with company data
        jobs = jobs.map(job => ({
          ...job,
          company: job.company_name ? {
            id: job.company_id,
            name: job.company_name,
            description: job.company_description,
            website: job.company_website,
            logo_url: job.company_logo_url,
            location: job.company_location
          } : null
        }));
      }

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          jobs,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalCount,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });
    } catch (error) {
      console.error('Get jobs error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get single job by ID
  async getJobById(req, res) {
    try {
      const { id } = req.params;
      const dbType = process.env.DB_TYPE || 'postgres';

      let job;

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        const companiesCollection = await getCollection('companies');

        job = await jobsCollection.findOne({ _id: id });
        if (job && job.company_id) {
          job.company = await companiesCollection.findOne({ _id: job.company_id });
        }
      } else {
        const query = `
          SELECT j.*, c.name as company_name, c.description as company_description,
                 c.website as company_website, c.logo_url as company_logo_url,
                 c.location as company_location
          FROM jobs j
          LEFT JOIN companies c ON j.company_id = c.id
          WHERE j.id = ?
        `;

        const jobs = await executeQuery(query, [id]);
        if (jobs.length === 0) {
          return res.status(404).json({
            success: false,
            message: 'Job not found'
          });
        }

        job = jobs[0];
        if (job.company_name) {
          job.company = {
            id: job.company_id,
            name: job.company_name,
            description: job.company_description,
            website: job.company_website,
            logo_url: job.company_logo_url,
            location: job.company_location
          };
        }
      }

      if (!job) {
        return res.status(404).json({
          success: false,
          message: 'Job not found'
        });
      }

      res.json({
        success: true,
        data: { job }
      });
    } catch (error) {
      console.error('Get job by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Create new job
  async createJob(req, res) {
    try {
      const {
        title,
        company,
        location,
        salaryMin,
        salaryMax,
        jobType,
        category,
        isRemote = false,
        description,
        requirements,
        benefits,
        tags,
        companyWebsite
      } = req.body;

      const userId = req.user.id;
      const jobId = uuidv4();
      const dbType = process.env.DB_TYPE || 'postgres';

      // Create or get company
      let companyId = null;
      if (company) {
        companyId = await this.createOrGetCompany(company, companyWebsite);
      }

      const jobData = {
        id: jobId,
        title,
        description,
        location,
        salary_min: salaryMin || null,
        salary_max: salaryMax || null,
        job_type: jobType,
        category,
        is_remote: isRemote,
        requirements: requirements || null,
        benefits: benefits || null,
        tags: tags || null,
        company_id: companyId,
        posted_by: userId,
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        await jobsCollection.insertOne({ _id: jobId, ...jobData });
      } else {
        const insertQuery = `
          INSERT INTO jobs (id, title, description, location, salary_min, salary_max, 
                           job_type, category, is_remote, requirements, benefits, tags,
                           company_id, posted_by, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        await executeQuery(insertQuery, [
          jobId, title, description, location, salaryMin, salaryMax,
          jobType, category, isRemote, requirements, benefits, 
          JSON.stringify(tags), companyId, userId, 'active',
          new Date(), new Date()
        ]);
      }

      res.status(201).json({
        success: true,
        message: 'Job created successfully',
        data: { job: { id: jobId, ...jobData } }
      });
    } catch (error) {
      console.error('Create job error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update job
  async updateJob(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const userId = req.user.id;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if job exists and user owns it
      let existingJob;
      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        existingJob = await jobsCollection.findOne({ _id: id });
      } else {
        const jobs = await executeQuery('SELECT * FROM jobs WHERE id = ?', [id]);
        existingJob = jobs[0];
      }

      if (!existingJob) {
        return res.status(404).json({
          success: false,
          message: 'Job not found'
        });
      }

      if (existingJob.posted_by !== userId && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to update this job'
        });
      }

      // Update job
      updateData.updated_at = new Date();

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        await jobsCollection.updateOne({ _id: id }, { $set: updateData });
      } else {
        const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const values = Object.values(updateData);
        values.push(id);

        await executeQuery(`UPDATE jobs SET ${setClause} WHERE id = ?`, values);
      }

      res.json({
        success: true,
        message: 'Job updated successfully'
      });
    } catch (error) {
      console.error('Update job error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Delete job
  async deleteJob(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if job exists and user owns it
      let existingJob;
      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        existingJob = await jobsCollection.findOne({ _id: id });
      } else {
        const jobs = await executeQuery('SELECT * FROM jobs WHERE id = ?', [id]);
        existingJob = jobs[0];
      }

      if (!existingJob) {
        return res.status(404).json({
          success: false,
          message: 'Job not found'
        });
      }

      if (existingJob.posted_by !== userId && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to delete this job'
        });
      }

      // Delete job
      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        await jobsCollection.deleteOne({ _id: id });
      } else {
        await executeQuery('DELETE FROM jobs WHERE id = ?', [id]);
      }

      res.json({
        success: true,
        message: 'Job deleted successfully'
      });
    } catch (error) {
      console.error('Delete job error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get featured jobs
  async getFeaturedJobs(req, res) {
    try {
      const dbType = process.env.DB_TYPE || 'postgres';
      let jobs;

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        const companiesCollection = await getCollection('companies');

        jobs = await jobsCollection
          .find({ status: 'active' })
          .sort({ created_at: -1 })
          .limit(6)
          .toArray();

        // Get company details
        for (let job of jobs) {
          if (job.company_id) {
            job.company = await companiesCollection.findOne({ _id: job.company_id });
          }
        }
      } else {
        const query = `
          SELECT j.*, c.name as company_name, c.description as company_description,
                 c.website as company_website, c.logo_url as company_logo_url,
                 c.location as company_location
          FROM jobs j
          LEFT JOIN companies c ON j.company_id = c.id
          WHERE j.status = 'active'
          ORDER BY j.created_at DESC
          LIMIT 6
        `;

        jobs = await executeQuery(query);

        // Format jobs with company data
        jobs = jobs.map(job => ({
          ...job,
          company: job.company_name ? {
            id: job.company_id,
            name: job.company_name,
            description: job.company_description,
            website: job.company_website,
            logo_url: job.company_logo_url,
            location: job.company_location
          } : null
        }));
      }

      res.json({
        success: true,
        data: { jobs }
      });
    } catch (error) {
      console.error('Get featured jobs error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Helper method to create or get company
  async createOrGetCompany(companyName, website = null) {
    try {
      const dbType = process.env.DB_TYPE || 'postgres';
      
      // Check if company exists
      let existingCompany;
      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        existingCompany = await companiesCollection.findOne({ name: companyName });
      } else {
        const companies = await executeQuery('SELECT * FROM companies WHERE name = ?', [companyName]);
        existingCompany = companies[0];
      }

      if (existingCompany) {
        return existingCompany.id || existingCompany._id;
      }

      // Create new company
      const companyId = uuidv4();
      const companyData = {
        id: companyId,
        name: companyName,
        website: website || null,
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        await companiesCollection.insertOne({ _id: companyId, ...companyData });
      } else {
        await executeQuery(
          'INSERT INTO companies (id, name, website, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
          [companyId, companyName, website, new Date(), new Date()]
        );
      }

      return companyId;
    } catch (error) {
      console.error('Create or get company error:', error);
      return null;
    }
  }
}

module.exports = new JobController();
