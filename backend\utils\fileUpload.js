const cloudinary = require('cloudinary').v2;
const multer = require('multer');
require('dotenv').config();

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Multer configuration for memory storage
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (req, file, cb) => {
  // Define allowed file types
  const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const allowedDocumentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ];

  const allAllowedTypes = [...allowedImageTypes, ...allowedDocumentTypes];

  if (allAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) and documents (PDF, DOC, DOCX, TXT) are allowed.'), false);
  }
};

// Multer upload configuration
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Upload to Cloudinary
const uploadToCloudinary = (buffer, options = {}) => {
  return new Promise((resolve, reject) => {
    const uploadOptions = {
      resource_type: 'auto',
      ...options,
    };

    cloudinary.uploader.upload_stream(
      uploadOptions,
      (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      }
    ).end(buffer);
  });
};

// Delete from Cloudinary
const deleteFromCloudinary = (publicId, resourceType = 'image') => {
  return new Promise((resolve, reject) => {
    cloudinary.uploader.destroy(
      publicId,
      { resource_type: resourceType },
      (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      }
    );
  });
};

// Upload middleware for single file
const uploadSingle = (fieldName) => {
  return upload.single(fieldName);
};

// Upload middleware for multiple files
const uploadMultiple = (fieldName, maxCount = 5) => {
  return upload.array(fieldName, maxCount);
};

// Upload middleware for multiple fields
const uploadFields = (fields) => {
  return upload.fields(fields);
};

// Avatar upload middleware
const uploadAvatar = uploadSingle('avatar');

// Resume upload middleware
const uploadResume = uploadSingle('resume');

// Company logo upload middleware
const uploadCompanyLogo = uploadSingle('logo');

// Validate file size
const validateFileSize = (maxSize) => {
  return (req, res, next) => {
    if (req.file && req.file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: `File size too large. Maximum size allowed is ${maxSize / (1024 * 1024)}MB`
      });
    }
    next();
  };
};

// Validate image dimensions
const validateImageDimensions = (minWidth, minHeight, maxWidth, maxHeight) => {
  return async (req, res, next) => {
    if (req.file && req.file.mimetype.startsWith('image/')) {
      try {
        // You would need to use a library like 'sharp' to get image dimensions
        // For now, we'll skip this validation
        next();
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: 'Invalid image file'
        });
      }
    } else {
      next();
    }
  };
};

// Generate unique filename
const generateUniqueFilename = (originalName, userId) => {
  const timestamp = Date.now();
  const extension = originalName.split('.').pop();
  return `${userId}_${timestamp}.${extension}`;
};

// Get file type from mimetype
const getFileType = (mimetype) => {
  if (mimetype.startsWith('image/')) return 'image';
  if (mimetype === 'application/pdf') return 'pdf';
  if (mimetype.includes('word') || mimetype.includes('document')) return 'document';
  if (mimetype === 'text/plain') return 'text';
  return 'unknown';
};

// File upload error handler
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size allowed is 10MB'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum allowed is 5 files'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Unexpected file field'
      });
    }
  }

  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }

  return res.status(500).json({
    success: false,
    message: 'File upload error'
  });
};

// Cleanup temporary files (if using disk storage)
const cleanupTempFiles = (files) => {
  if (!files) return;

  const fileArray = Array.isArray(files) ? files : [files];
  
  fileArray.forEach(file => {
    if (file.path) {
      const fs = require('fs');
      fs.unlink(file.path, (err) => {
        if (err) console.error('Error deleting temp file:', err);
      });
    }
  });
};

// Verify Cloudinary configuration
const verifyCloudinaryConfig = () => {
  const requiredEnvVars = ['CLOUDINARY_CLOUD_NAME', 'CLOUDINARY_API_KEY', 'CLOUDINARY_API_SECRET'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing Cloudinary configuration:', missingVars);
    return false;
  }

  console.log('✅ Cloudinary configuration verified');
  return true;
};

module.exports = {
  upload,
  uploadToCloudinary,
  deleteFromCloudinary,
  uploadSingle,
  uploadMultiple,
  uploadFields,
  uploadAvatar,
  uploadResume,
  uploadCompanyLogo,
  validateFileSize,
  validateImageDimensions,
  generateUniqueFilename,
  getFileType,
  handleUploadError,
  cleanupTempFiles,
  verifyCloudinaryConfig
};
