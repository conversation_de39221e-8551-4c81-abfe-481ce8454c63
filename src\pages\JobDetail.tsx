import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { MapPin, Clock, Users, Briefcase, Building, DollarSign, Share, Heart } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { JobApplicationDialog } from "@/components/JobApplicationDialog";
import { UserMenu } from "@/components/UserMenu";

// Mock job data (in a real app, this would come from an API)
const jobDetails = {
  1: {
    id: 1,
    title: "Senior Frontend Developer",
    company: "TechCorp Solutions",
    location: "San Francisco, CA",
    salary: "$120,000 - $180,000",
    type: "Full-time",
    remote: true,
    description: "Join our team to build next-generation web applications using React and TypeScript. We're looking for an experienced developer to lead frontend initiatives and mentor junior developers.",
    tags: ["React", "TypeScript", "Remote"],
    postedTime: "2 days ago",
    applicants: 45,
    category: "Engineering",
    requirements: [
      "5+ years of experience with React and modern JavaScript",
      "Strong proficiency in TypeScript",
      "Experience with state management libraries (Redux, Zustand)",
      "Knowledge of modern build tools (Webpack, Vite)",
      "Experience with testing frameworks (Jest, React Testing Library)",
      "Understanding of responsive design and CSS frameworks",
      "Experience with version control (Git) and CI/CD pipelines"
    ],
    responsibilities: [
      "Lead frontend development initiatives and architecture decisions",
      "Mentor junior developers and conduct code reviews",
      "Collaborate with design and product teams to implement user interfaces",
      "Optimize applications for maximum speed and scalability",
      "Participate in agile development processes and sprint planning",
      "Stay up-to-date with emerging technologies and industry trends",
      "Contribute to technical documentation and best practices"
    ],
    benefits: [
      "Competitive salary and equity package",
      "Comprehensive health, dental, and vision insurance",
      "Flexible working hours and remote work options",
      "Professional development budget ($3,000/year)",
      "Unlimited PTO policy",
      "State-of-the-art equipment and home office setup",
      "Team retreats and company events"
    ],
    companyInfo: {
      size: "100-500 employees",
      industry: "Technology",
      founded: "2018",
      description: "TechCorp Solutions is a fast-growing technology company focused on building innovative web applications that solve real-world problems. We pride ourselves on our collaborative culture and commitment to technical excellence."
    }
  }
};

const JobDetail = () => {
  const { id } = useParams();
  const job = jobDetails[Number(id) as keyof typeof jobDetails];

  if (!job) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Job Not Found</h1>
          <p className="text-gray-600 mb-4">The job you're looking for doesn't exist.</p>
          <Link to="/jobs">
            <Button>Back to Jobs</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center">
              <Briefcase className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                JobPortal
              </span>
            </Link>
            <div className="flex items-center space-x-4">
              <Link to="/jobs" className="text-gray-700 hover:text-blue-600 transition-colors">
                Find Jobs
              </Link>
              <Link to="/post-job" className="text-gray-700 hover:text-blue-600 transition-colors">
                Post a Job
              </Link>
              <UserMenu />
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Job Header */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-3xl text-gray-900 mb-2">{job.title}</CardTitle>
                    <p className="text-xl font-semibold text-blue-600 mb-4">{job.company}</p>
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {job.location}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {job.postedTime}
                      </div>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {job.applicants} applicants
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        {job.salary}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant={job.remote ? "default" : "secondary"} className="bg-green-100 text-green-800">
                        {job.remote ? "Remote" : "On-site"}
                      </Badge>
                      <Badge variant="outline">{job.type}</Badge>
                      <Badge variant="outline">{job.category}</Badge>
                      {job.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="icon">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Share className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Job Description */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Job Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{job.description}</p>
              </CardContent>
            </Card>

            {/* Requirements */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {job.requirements.map((req, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-gray-700">{req}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Responsibilities */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Responsibilities</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {job.responsibilities.map((resp, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-2 h-2 bg-purple-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-gray-700">{resp}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Benefits & Perks</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {job.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Apply Card */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm sticky top-24">
              <CardHeader>
                <CardTitle className="text-center">Ready to Apply?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <JobApplicationDialog 
                  jobId={job.id.toString()}
                  jobTitle={job.title}
                  companyName={job.company}
                />
                <Button variant="outline" className="w-full border-blue-200 text-blue-600 hover:bg-blue-50">
                  Save for Later
                </Button>
                <Separator />
                <div className="text-center text-sm text-gray-600">
                  <p className="mb-2">Share this job with others</p>
                  <div className="flex justify-center space-x-2">
                    <Button variant="outline" size="sm">LinkedIn</Button>
                    <Button variant="outline" size="sm">Twitter</Button>
                    <Button variant="outline" size="sm">Email</Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Info */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  About {job.company}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-gray-700 text-sm">{job.companyInfo.description}</p>
                <Separator />
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-900">Industry:</span>
                    <p className="text-gray-600">{job.companyInfo.industry}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-900">Size:</span>
                    <p className="text-gray-600">{job.companyInfo.size}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-900">Founded:</span>
                    <p className="text-gray-600">{job.companyInfo.founded}</p>
                  </div>
                </div>
                <Button variant="outline" className="w-full mt-4">
                  View Company Profile
                </Button>
              </CardContent>
            </Card>

            {/* Similar Jobs */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Similar Jobs</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors cursor-pointer">
                    <h4 className="font-medium text-gray-900">Frontend Developer</h4>
                    <p className="text-sm text-gray-600">StartupCo</p>
                    <p className="text-sm text-green-600">$80k - $120k</p>
                  </div>
                  <div className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors cursor-pointer">
                    <h4 className="font-medium text-gray-900">React Developer</h4>
                    <p className="text-sm text-gray-600">WebTech Inc</p>
                    <p className="text-sm text-green-600">$100k - $140k</p>
                  </div>
                  <div className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors cursor-pointer">
                    <h4 className="font-medium text-gray-900">Full Stack Developer</h4>
                    <p className="text-sm text-gray-600">DevStudio</p>
                    <p className="text-sm text-green-600">$110k - $160k</p>
                  </div>
                </div>
                <Link to="/jobs">
                  <Button variant="outline" className="w-full">
                    View More Jobs
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetail;
