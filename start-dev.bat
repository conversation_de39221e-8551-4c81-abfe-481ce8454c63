@echo off
echo.
echo ========================================
echo   Career Link Spark - Development
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is installed

REM Check if we're in the right directory
if not exist "backend" (
    echo ❌ Backend folder not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

if not exist "src" (
    echo ❌ Frontend src folder not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo ✅ Project structure verified

REM Install frontend dependencies if needed
if not exist "node_modules" (
    echo.
    echo 📦 Installing frontend dependencies...
    call npm install
    if errorlevel 1 (
        echo ❌ Failed to install frontend dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ Frontend dependencies already installed
)

REM Install backend dependencies if needed
if not exist "backend\node_modules" (
    echo.
    echo 📦 Installing backend dependencies...
    cd backend
    call npm install
    if errorlevel 1 (
        echo ❌ Failed to install backend dependencies
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ Backend dependencies already installed
)

REM Check if backend .env exists
if not exist "backend\.env" (
    echo.
    echo ⚠️  Backend .env file not found!
    echo Please copy .env.example to .env and configure it:
    echo.
    echo   cd backend
    echo   copy .env.example .env
    echo.
    echo Then edit the .env file with your database and service configurations.
    echo.
    pause
    exit /b 1
)

echo ✅ Backend .env file found

echo.
echo 🚀 Starting development servers...
echo.
echo Frontend: http://localhost:5173
echo Backend:  http://localhost:5000
echo API Docs: http://localhost:5000/api/docs
echo.
echo Press Ctrl+C to stop both servers
echo.

REM Start both servers using Node.js script
node run-dev.js
