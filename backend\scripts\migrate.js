const { executeQuery, connectDatabase } = require('../config/database');
require('dotenv').config();

// PostgreSQL migration scripts
const postgresqlMigrations = {
  // Create users table
  createUsersTable: `
    CREATE TABLE IF NOT EXISTS users (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      email VARCHAR(255) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      full_name VA<PERSON>HA<PERSON>(255),
      role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'employer', 'admin')),
      is_verified BOOLEAN DEFAULT FALSE,
      last_login TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Create companies table
  createCompaniesTable: `
    CREATE TABLE IF NOT EXISTS companies (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) UNIQUE NOT NULL,
      description TEXT,
      website VARCHAR(255),
      logo_url VARCHAR(500),
      location VARCHAR(255),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Create jobs table
  createJobsTable: `
    CREATE TABLE IF NOT EXISTS jobs (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      title VARCHAR(255) NOT NULL,
      description TEXT NOT NULL,
      location VARCHAR(255) NOT NULL,
      salary_min INTEGER,
      salary_max INTEGER,
      job_type VARCHAR(50) NOT NULL CHECK (job_type IN ('Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship')),
      category VARCHAR(100) NOT NULL,
      is_remote BOOLEAN DEFAULT FALSE,
      requirements TEXT,
      benefits TEXT,
      tags JSONB,
      company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
      posted_by UUID REFERENCES users(id) ON DELETE CASCADE,
      status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'closed')),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Create profiles table
  createProfilesTable: `
    CREATE TABLE IF NOT EXISTS profiles (
      id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
      full_name VARCHAR(255),
      email VARCHAR(255),
      phone VARCHAR(20),
      location VARCHAR(255),
      bio TEXT,
      skills JSONB,
      experience_years INTEGER,
      resume_url VARCHAR(500),
      avatar_url VARCHAR(500),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // Create job_applications table
  createJobApplicationsTable: `
    CREATE TABLE IF NOT EXISTS job_applications (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      applicant_name VARCHAR(255) NOT NULL,
      applicant_email VARCHAR(255) NOT NULL,
      cover_letter TEXT,
      resume_url VARCHAR(500),
      status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'accepted', 'rejected')),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(job_id, user_id)
    );
  `,

  // Create indexes
  createIndexes: `
    CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
    CREATE INDEX IF NOT EXISTS idx_jobs_category ON jobs(category);
    CREATE INDEX IF NOT EXISTS idx_jobs_job_type ON jobs(job_type);
    CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs(location);
    CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at);
    CREATE INDEX IF NOT EXISTS idx_jobs_company_id ON jobs(company_id);
    CREATE INDEX IF NOT EXISTS idx_jobs_posted_by ON jobs(posted_by);
    CREATE INDEX IF NOT EXISTS idx_job_applications_job_id ON job_applications(job_id);
    CREATE INDEX IF NOT EXISTS idx_job_applications_user_id ON job_applications(user_id);
    CREATE INDEX IF NOT EXISTS idx_job_applications_status ON job_applications(status);
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
  `
};

// MySQL migration scripts
const mysqlMigrations = {
  // Create users table
  createUsersTable: `
    CREATE TABLE IF NOT EXISTS users (
      id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
      email VARCHAR(255) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      full_name VARCHAR(255),
      role ENUM('user', 'employer', 'admin') DEFAULT 'user',
      is_verified BOOLEAN DEFAULT FALSE,
      last_login TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
  `,

  // Create companies table
  createCompaniesTable: `
    CREATE TABLE IF NOT EXISTS companies (
      id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
      name VARCHAR(255) UNIQUE NOT NULL,
      description TEXT,
      website VARCHAR(255),
      logo_url VARCHAR(500),
      location VARCHAR(255),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
  `,

  // Create jobs table
  createJobsTable: `
    CREATE TABLE IF NOT EXISTS jobs (
      id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
      title VARCHAR(255) NOT NULL,
      description TEXT NOT NULL,
      location VARCHAR(255) NOT NULL,
      salary_min INT,
      salary_max INT,
      job_type ENUM('Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship') NOT NULL,
      category VARCHAR(100) NOT NULL,
      is_remote BOOLEAN DEFAULT FALSE,
      requirements TEXT,
      benefits TEXT,
      tags JSON,
      company_id VARCHAR(36),
      posted_by VARCHAR(36),
      status ENUM('active', 'inactive', 'closed') DEFAULT 'active',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL,
      FOREIGN KEY (posted_by) REFERENCES users(id) ON DELETE CASCADE
    );
  `,

  // Create profiles table
  createProfilesTable: `
    CREATE TABLE IF NOT EXISTS profiles (
      id VARCHAR(36) PRIMARY KEY,
      full_name VARCHAR(255),
      email VARCHAR(255),
      phone VARCHAR(20),
      location VARCHAR(255),
      bio TEXT,
      skills JSON,
      experience_years INT,
      resume_url VARCHAR(500),
      avatar_url VARCHAR(500),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
    );
  `,

  // Create job_applications table
  createJobApplicationsTable: `
    CREATE TABLE IF NOT EXISTS job_applications (
      id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
      job_id VARCHAR(36),
      user_id VARCHAR(36),
      applicant_name VARCHAR(255) NOT NULL,
      applicant_email VARCHAR(255) NOT NULL,
      cover_letter TEXT,
      resume_url VARCHAR(500),
      status ENUM('pending', 'reviewed', 'accepted', 'rejected') DEFAULT 'pending',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      UNIQUE KEY unique_application (job_id, user_id)
    );
  `,

  // Create indexes
  createIndexes: `
    CREATE INDEX idx_jobs_status ON jobs(status);
    CREATE INDEX idx_jobs_category ON jobs(category);
    CREATE INDEX idx_jobs_job_type ON jobs(job_type);
    CREATE INDEX idx_jobs_location ON jobs(location);
    CREATE INDEX idx_jobs_created_at ON jobs(created_at);
    CREATE INDEX idx_jobs_company_id ON jobs(company_id);
    CREATE INDEX idx_jobs_posted_by ON jobs(posted_by);
    CREATE INDEX idx_job_applications_job_id ON job_applications(job_id);
    CREATE INDEX idx_job_applications_user_id ON job_applications(user_id);
    CREATE INDEX idx_job_applications_status ON job_applications(status);
    CREATE INDEX idx_users_email ON users(email);
    CREATE INDEX idx_companies_name ON companies(name);
  `
};

// MongoDB collections setup
const setupMongoDB = async () => {
  const { getCollection } = require('../config/database');
  
  try {
    console.log('Setting up MongoDB collections...');

    // Create collections with validation
    const db = require('../config/database').mongoDb;

    // Users collection
    await db.createCollection('users', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['email', 'password'],
          properties: {
            email: { bsonType: 'string' },
            password: { bsonType: 'string' },
            full_name: { bsonType: 'string' },
            role: { enum: ['user', 'employer', 'admin'] },
            is_verified: { bsonType: 'bool' },
            created_at: { bsonType: 'date' },
            updated_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Create indexes
    const usersCollection = await getCollection('users');
    await usersCollection.createIndex({ email: 1 }, { unique: true });

    const jobsCollection = await getCollection('jobs');
    await jobsCollection.createIndex({ status: 1 });
    await jobsCollection.createIndex({ category: 1 });
    await jobsCollection.createIndex({ job_type: 1 });
    await jobsCollection.createIndex({ location: 1 });
    await jobsCollection.createIndex({ created_at: -1 });
    await jobsCollection.createIndex({ company_id: 1 });
    await jobsCollection.createIndex({ posted_by: 1 });

    const applicationsCollection = await getCollection('job_applications');
    await applicationsCollection.createIndex({ job_id: 1, user_id: 1 }, { unique: true });
    await applicationsCollection.createIndex({ job_id: 1 });
    await applicationsCollection.createIndex({ user_id: 1 });
    await applicationsCollection.createIndex({ status: 1 });

    const companiesCollection = await getCollection('companies');
    await companiesCollection.createIndex({ name: 1 }, { unique: true });

    console.log('✅ MongoDB collections and indexes created successfully');
  } catch (error) {
    console.error('❌ MongoDB setup error:', error);
    throw error;
  }
};

// Run migrations
const runMigrations = async () => {
  try {
    console.log('🔄 Starting database migrations...');

    await connectDatabase();
    const dbType = process.env.DB_TYPE || 'postgres';

    if (dbType === 'mongodb') {
      await setupMongoDB();
    } else {
      const migrations = dbType === 'mysql' ? mysqlMigrations : postgresqlMigrations;

      console.log(`Running ${dbType.toUpperCase()} migrations...`);

      // Run migrations in order
      await executeQuery(migrations.createUsersTable);
      console.log('✅ Users table created');

      await executeQuery(migrations.createCompaniesTable);
      console.log('✅ Companies table created');

      await executeQuery(migrations.createJobsTable);
      console.log('✅ Jobs table created');

      await executeQuery(migrations.createProfilesTable);
      console.log('✅ Profiles table created');

      await executeQuery(migrations.createJobApplicationsTable);
      console.log('✅ Job applications table created');

      // Create indexes (handle errors gracefully as some might already exist)
      try {
        await executeQuery(migrations.createIndexes);
        console.log('✅ Indexes created');
      } catch (error) {
        console.log('⚠️  Some indexes might already exist:', error.message);
      }
    }

    console.log('🎉 Database migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigrations };
