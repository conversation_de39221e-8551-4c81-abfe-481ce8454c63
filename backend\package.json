{"name": "career-link-spark-backend", "version": "1.0.0", "description": "Backend API for Career Link Spark job portal", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "setup": "npm run migrate && npm run seed"}, "keywords": ["job-portal", "api", "express", "nodejs"], "author": "Career Link Spark Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}