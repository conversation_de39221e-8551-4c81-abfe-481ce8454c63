const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { verifyToken } = require('../middleware/auth');
const {
  validateUserRegistration,
  validateUserLogin,
  validatePasswordReset,
  validatePasswordUpdate
} = require('../middleware/validation');

// @route   POST /api/auth/register
// @desc    Register new user
// @access  Public
router.post('/register', validateUserRegistration, authController.register);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', validateUserLogin, authController.login);

// @route   POST /api/auth/refresh-token
// @desc    Refresh access token
// @access  Public
router.post('/refresh-token', authController.refreshToken);

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', verifyToken, authController.getCurrentUser);

// @route   POST /api/auth/logout
// @desc    Logout user
// @access  Private
router.post('/logout', verifyToken, authController.logout);

// @route   POST /api/auth/forgot-password
// @desc    Send password reset email
// @access  Public
router.post('/forgot-password', validatePasswordReset, async (req, res) => {
  try {
    const { email } = req.body;
    const { executeQuery, getCollection } = require('../config/database');
    const { sendPasswordResetEmail } = require('../utils/emailService');
    const { generateToken } = require('../middleware/auth');
    const dbType = process.env.DB_TYPE || 'postgres';

    // Find user by email
    let user;
    if (dbType === 'mongodb') {
      const usersCollection = await getCollection('users');
      user = await usersCollection.findOne({ email });
    } else {
      const query = dbType === 'mysql' 
        ? 'SELECT * FROM users WHERE email = ?'
        : 'SELECT * FROM users WHERE email = $1';
      const users = await executeQuery(query, [email]);
      user = users[0];
    }

    if (!user) {
      // Don't reveal if user exists or not for security
      return res.json({
        success: true,
        message: 'If an account with that email exists, we have sent a password reset link.'
      });
    }

    // Generate reset token (expires in 1 hour)
    const resetToken = generateToken(user.id, user.role, '1h');
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    // Send reset email
    await sendPasswordResetEmail(user.email, user.full_name, resetLink);

    res.json({
      success: true,
      message: 'If an account with that email exists, we have sent a password reset link.'
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   POST /api/auth/reset-password
// @desc    Reset password with token
// @access  Public
router.post('/reset-password', async (req, res) => {
  try {
    const { token, newPassword } = req.body;
    const jwt = require('jsonwebtoken');
    const bcrypt = require('bcryptjs');
    const { executeQuery, getCollection } = require('../config/database');

    if (!token || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Token and new password are required'
      });
    }

    // Verify token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update user password
    const dbType = process.env.DB_TYPE || 'postgres';
    
    if (dbType === 'mongodb') {
      const usersCollection = await getCollection('users');
      await usersCollection.updateOne(
        { _id: decoded.userId },
        { $set: { password: hashedPassword, updated_at: new Date() } }
      );
    } else {
      const updateQuery = dbType === 'mysql'
        ? 'UPDATE users SET password = ?, updated_at = ? WHERE id = ?'
        : 'UPDATE users SET password = $1, updated_at = $2 WHERE id = $3';
      
      await executeQuery(updateQuery, [hashedPassword, new Date(), decoded.userId]);
    }

    res.json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   POST /api/auth/change-password
// @desc    Change password for authenticated user
// @access  Private
router.post('/change-password', verifyToken, validatePasswordUpdate, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;
    const bcrypt = require('bcryptjs');
    const { executeQuery, getCollection } = require('../config/database');

    // Get current user
    const dbType = process.env.DB_TYPE || 'postgres';
    let user;
    
    if (dbType === 'mongodb') {
      const usersCollection = await getCollection('users');
      user = await usersCollection.findOne({ _id: userId });
    } else {
      const query = dbType === 'mysql' 
        ? 'SELECT * FROM users WHERE id = ?'
        : 'SELECT * FROM users WHERE id = $1';
      const users = await executeQuery(query, [userId]);
      user = users[0];
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    if (dbType === 'mongodb') {
      const usersCollection = await getCollection('users');
      await usersCollection.updateOne(
        { _id: userId },
        { $set: { password: hashedPassword, updated_at: new Date() } }
      );
    } else {
      const updateQuery = dbType === 'mysql'
        ? 'UPDATE users SET password = ?, updated_at = ? WHERE id = ?'
        : 'UPDATE users SET password = $1, updated_at = $2 WHERE id = $3';
      
      await executeQuery(updateQuery, [hashedPassword, new Date(), userId]);
    }

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   POST /api/auth/verify-email
// @desc    Verify email address
// @access  Public
router.post('/verify-email', async (req, res) => {
  try {
    const { token } = req.body;
    const jwt = require('jsonwebtoken');
    const { executeQuery, getCollection } = require('../config/database');

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Verification token is required'
      });
    }

    // Verify token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
    }

    // Update user verification status
    const dbType = process.env.DB_TYPE || 'postgres';
    
    if (dbType === 'mongodb') {
      const usersCollection = await getCollection('users');
      await usersCollection.updateOne(
        { _id: decoded.userId },
        { $set: { is_verified: true, updated_at: new Date() } }
      );
    } else {
      const updateQuery = dbType === 'mysql'
        ? 'UPDATE users SET is_verified = ?, updated_at = ? WHERE id = ?'
        : 'UPDATE users SET is_verified = $1, updated_at = $2 WHERE id = $3';
      
      await executeQuery(updateQuery, [true, new Date(), decoded.userId]);
    }

    res.json({
      success: true,
      message: 'Email verified successfully'
    });
  } catch (error) {
    console.error('Verify email error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
