import { useMutation, useQueryClient } from "@tanstack/react-query";
import { authService } from "@/services/authService";
import { useToast } from "@/hooks/use-toast";
import type { SignUpData, SignInData, ResetPasswordData, UpdatePasswordData } from "@/services/authService";

export const useSignUp = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SignUpData) => {
      const result = await authService.signUp(data);

      if (!result.success) {
        throw new Error(result.error || 'Failed to sign up');
      }

      return result;
    },
    onSuccess: (data) => {
      if (data.user) {
        toast({
          title: "Account created!",
          description: "Please check your email to verify your account.",
        });
      }
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    },
    onError: (error: any) => {
      toast({
        title: "Sign up failed",
        description: error.message || "Failed to create account. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useSignIn = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SignInData) => {
      const result = await authService.signIn(data);

      if (!result.success) {
        throw new Error(result.error || 'Failed to sign in');
      }

      return result;
    },
    onSuccess: () => {
      toast({
        title: "Welcome back!",
        description: "You have been successfully signed in.",
      });
      queryClient.invalidateQueries({ queryKey: ['auth'] });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (error: any) => {
      toast({
        title: "Sign in failed",
        description: error.message || "Failed to sign in. Please check your credentials.",
        variant: "destructive",
      });
    },
  });
};

export const useSignOut = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const result = await authService.signOut();

      if (!result.success) {
        throw new Error(result.error || 'Failed to sign out');
      }

      return result;
    },
    onSuccess: () => {
      toast({
        title: "Signed out",
        description: "You have been successfully signed out.",
      });
      queryClient.clear(); // Clear all cached data
    },
    onError: (error: any) => {
      toast({
        title: "Sign out failed",
        description: error.message || "Failed to sign out. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useResetPassword = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: ResetPasswordData) => {
      const result = await authService.resetPassword(data);

      if (!result.success) {
        throw new Error(result.error || 'Failed to reset password');
      }

      return result;
    },
    onSuccess: () => {
      toast({
        title: "Reset email sent!",
        description: "Please check your email for password reset instructions.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Reset failed",
        description: error.message || "Failed to send reset email. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdatePassword = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: UpdatePasswordData) => {
      const result = await authService.updatePassword(data);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update password');
      }

      return result;
    },
    onSuccess: () => {
      toast({
        title: "Password updated!",
        description: "Your password has been successfully updated.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Update failed",
        description: error.message || "Failed to update password. Please try again.",
        variant: "destructive",
      });
    },
  });
};
