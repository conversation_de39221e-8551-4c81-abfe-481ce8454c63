# Backend Services Documentation

This directory contains all the backend service files for the Career Link Spark job portal application. Each service handles specific domain operations and provides a clean interface between the frontend components and the Supabase backend.

## Architecture Overview

The backend is organized into modular services, each responsible for a specific domain:

- **Authentication Service** - User authentication and session management
- **Job Service** - Job posting, retrieval, and management
- **Job Application Service** - Job application operations
- **Company Service** - Company information management
- **Profile Service** - User profile management

## Services

### 1. Authentication Service (`authService.ts`)

Handles all authentication-related operations:

- User sign up and sign in
- Password reset and update
- Session management
- User profile creation on signup

**Key Methods:**
- `signUp(data)` - Register new user
- `signIn(data)` - Authenticate user
- `signOut()` - Sign out current user
- `getCurrentUser()` - Get current authenticated user
- `resetPassword(email)` - Send password reset email

### 2. Job Service (`jobService.ts`)

Manages job postings and job-related operations:

- Create, read, update, delete jobs
- Job filtering and search
- Featured jobs retrieval
- Company association

**Key Methods:**
- `getJobs(filters, page, pageSize)` - Get paginated jobs with filters
- `getJobById(id)` - Get single job details
- `createJob(jobData)` - Create new job posting
- `updateJob(jobData)` - Update existing job
- `getFeaturedJobs()` - Get featured jobs for homepage

### 3. Job Application Service (`jobApplicationService.ts`)

Handles job application operations:

- Submit job applications
- Track application status
- Application statistics
- Employer application management

**Key Methods:**
- `createApplication(data, userId, email, name)` - Submit job application
- `getApplicationsByUser(userId)` - Get user's applications
- `getApplicationsByJob(jobId)` - Get applications for a job
- `updateApplication(data)` - Update application status
- `getUserApplicationStats(userId)` - Get application statistics

### 4. Company Service (`companyService.ts`)

Manages company information:

- Company CRUD operations
- Company job listings
- Company statistics

**Key Methods:**
- `getCompanies(page, pageSize, search)` - Get paginated companies
- `getCompanyById(id)` - Get company details
- `createCompany(data)` - Create new company
- `getCompanyJobs(companyId)` - Get jobs for a company
- `getCompanyStats(companyId)` - Get company statistics

### 5. Profile Service (`profileService.ts`)

Handles user profile management:

- Profile CRUD operations
- File uploads (avatar, resume)
- Profile search
- Profile completion tracking

**Key Methods:**
- `getProfile(userId)` - Get user profile
- `updateProfile(userId, data)` - Update profile
- `uploadAvatar(userId, file)` - Upload profile avatar
- `uploadResume(userId, file)` - Upload resume
- `searchProfiles(query)` - Search user profiles

## Hooks

Each service has corresponding React hooks in the `/hooks` directory:

### Authentication Hooks (`useAuth.ts`)
- `useSignUp()` - Sign up mutation
- `useSignIn()` - Sign in mutation
- `useSignOut()` - Sign out mutation
- `useResetPassword()` - Password reset mutation

### Job Hooks (`useJobs.ts`)
- `useJobs(filters, page, pageSize)` - Get jobs query
- `useJob(id)` - Get single job query
- `useFeaturedJobs()` - Get featured jobs query

### Job Application Hooks (`useJobApplication.ts`)
- `useJobApplication()` - Submit application mutation
- `useUserApplications()` - Get user applications query
- `useJobApplications(jobId)` - Get job applications query
- `useJobApplicationStats()` - Get application statistics

### Company Hooks (`useCompanies.ts`)
- `useCompanies()` - Get companies query
- `useCompany(id)` - Get single company query
- `useCreateCompany()` - Create company mutation

### Profile Hooks (`useProfile.ts`)
- `useProfile(userId)` - Get profile query
- `useUpdateProfile()` - Update profile mutation
- `useUploadAvatar()` - Upload avatar mutation
- `useUploadResume()` - Upload resume mutation

### Job Posting Hooks (`usePostJob.ts`)
- `useCreateJob()` - Create job mutation
- `useUpdateJob()` - Update job mutation
- `useDeleteJob()` - Delete job mutation

## Types

All TypeScript types and interfaces are defined in `types.ts`:

- Database table types from Supabase
- Service request/response types
- Extended types with relations
- Error handling types
- Validation types

## Error Handling

All services use consistent error handling:

- Custom `ServiceError` class for structured errors
- Standardized `ServiceResponse<T>` interface
- Proper error propagation to React Query
- User-friendly error messages in hooks

## Usage Examples

### Creating a Job
```typescript
import { useCreateJob } from '@/hooks/usePostJob';

const CreateJobComponent = () => {
  const createJob = useCreateJob();
  
  const handleSubmit = (jobData) => {
    createJob.mutate(jobData, {
      onSuccess: () => {
        // Handle success
      }
    });
  };
};
```

### Fetching Jobs with Filters
```typescript
import { useJobs } from '@/hooks/useJobs';

const JobsComponent = () => {
  const filters = { search: 'developer', location: 'San Francisco' };
  const { data, isLoading } = useJobs(filters, 1, 10);
  
  return (
    <div>
      {data?.data.map(job => (
        <JobCard key={job.id} job={job} />
      ))}
    </div>
  );
};
```

### Submitting Job Application
```typescript
import { useJobApplication } from '@/hooks/useJobApplication';

const ApplyComponent = ({ jobId }) => {
  const jobApplication = useJobApplication();
  
  const handleApply = (coverLetter) => {
    jobApplication.mutate({ jobId, coverLetter });
  };
};
```

## Database Schema

The services work with the following Supabase tables:

- `jobs` - Job postings
- `companies` - Company information
- `job_applications` - Job applications
- `profiles` - User profiles

## Security

- All operations respect Row Level Security (RLS) policies
- User authentication required for protected operations
- Input validation on all service methods
- Proper error handling to prevent information leakage

## Performance

- Efficient pagination for large datasets
- Query optimization with proper indexing
- Caching through React Query
- Lazy loading of related data

## Future Enhancements

- Real-time notifications for job applications
- Advanced search with Elasticsearch
- File upload optimization
- Bulk operations for employers
- Analytics and reporting features
