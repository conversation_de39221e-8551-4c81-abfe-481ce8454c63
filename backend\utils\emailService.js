const nodemailer = require('nodemailer');
require('dotenv').config();

// Email templates
const emailTemplates = {
  welcome: {
    subject: 'Welcome to Career Link Spark!',
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome to Career Link Spark!</h1>
        <p>Hi ${data.fullName},</p>
        <p>Thank you for joining Career Link Spark! We're excited to help you find your dream job.</p>
        <p>To get started, please verify your email address by clicking the link below:</p>
        <a href="${data.verificationLink}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Verify Email Address
        </a>
        <p>If you didn't create this account, please ignore this email.</p>
        <p>Best regards,<br>The Career Link Spark Team</p>
      </div>
    `
  },
  
  'application-confirmation': {
    subject: 'Application Submitted Successfully',
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Application Submitted!</h1>
        <p>Hi ${data.applicantName},</p>
        <p>Your application for the position of <strong>${data.jobTitle}</strong> at <strong>${data.companyName}</strong> has been successfully submitted.</p>
        <p>We'll notify you as soon as there's an update on your application status.</p>
        <p>You can track your applications in your dashboard.</p>
        <p>Best of luck!</p>
        <p>Best regards,<br>The Career Link Spark Team</p>
      </div>
    `
  },
  
  'application-status-update': {
    subject: (data) => `Application Status Update - ${data.jobTitle}`,
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: ${data.status === 'accepted' ? '#059669' : data.status === 'rejected' ? '#dc2626' : '#2563eb'};">
          Application Status Update
        </h1>
        <p>Hi ${data.applicantName},</p>
        <p>We have an update on your application for <strong>${data.jobTitle}</strong>.</p>
        <p><strong>Status:</strong> ${data.status.charAt(0).toUpperCase() + data.status.slice(1)}</p>
        <p>${data.statusMessage}</p>
        ${data.status === 'accepted' ? '<p>Congratulations! 🎉</p>' : ''}
        <p>You can view more details in your dashboard.</p>
        <p>Best regards,<br>The Career Link Spark Team</p>
      </div>
    `
  },
  
  'password-reset': {
    subject: 'Password Reset Request',
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Password Reset Request</h1>
        <p>Hi ${data.fullName},</p>
        <p>We received a request to reset your password for your Career Link Spark account.</p>
        <p>Click the link below to reset your password:</p>
        <a href="${data.resetLink}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Reset Password
        </a>
        <p>This link will expire in 1 hour for security reasons.</p>
        <p>If you didn't request this password reset, please ignore this email.</p>
        <p>Best regards,<br>The Career Link Spark Team</p>
      </div>
    `
  }
};

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_PORT == 465, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
};

// Send email function
const sendEmail = async ({ to, subject, template, data, html, text }) => {
  try {
    const transporter = createTransporter();

    let emailContent = {};

    if (template && emailTemplates[template]) {
      const templateData = emailTemplates[template];
      emailContent.subject = typeof templateData.subject === 'function' 
        ? templateData.subject(data) 
        : templateData.subject;
      emailContent.html = templateData.html(data);
    } else {
      emailContent.subject = subject;
      emailContent.html = html;
      emailContent.text = text;
    }

    const mailOptions = {
      from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
      to,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    console.error('Email sending failed:', error);
    throw error;
  }
};

// Send bulk emails
const sendBulkEmails = async (emails) => {
  try {
    const transporter = createTransporter();
    const results = [];

    for (const email of emails) {
      try {
        const result = await sendEmail(email);
        results.push({ success: true, messageId: result.messageId, to: email.to });
      } catch (error) {
        results.push({ success: false, error: error.message, to: email.to });
      }
    }

    return results;
  } catch (error) {
    console.error('Bulk email sending failed:', error);
    throw error;
  }
};

// Verify email configuration
const verifyEmailConfig = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('✅ Email configuration verified successfully');
    return true;
  } catch (error) {
    console.error('❌ Email configuration verification failed:', error);
    return false;
  }
};

// Email queue (for production, consider using Redis or a proper queue system)
class EmailQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
  }

  add(emailData) {
    this.queue.push(emailData);
    if (!this.processing) {
      this.process();
    }
  }

  async process() {
    this.processing = true;

    while (this.queue.length > 0) {
      const emailData = this.queue.shift();
      try {
        await sendEmail(emailData);
        console.log(`Email sent to ${emailData.to}`);
      } catch (error) {
        console.error(`Failed to send email to ${emailData.to}:`, error);
        // Optionally, add back to queue for retry
      }
    }

    this.processing = false;
  }
}

const emailQueue = new EmailQueue();

// Helper functions for common email types
const sendWelcomeEmail = async (userEmail, fullName, verificationLink) => {
  return sendEmail({
    to: userEmail,
    template: 'welcome',
    data: { fullName, verificationLink }
  });
};

const sendApplicationConfirmation = async (applicantEmail, applicantName, jobTitle, companyName) => {
  return sendEmail({
    to: applicantEmail,
    template: 'application-confirmation',
    data: { applicantName, jobTitle, companyName }
  });
};

const sendApplicationStatusUpdate = async (applicantEmail, applicantName, jobTitle, status, statusMessage) => {
  return sendEmail({
    to: applicantEmail,
    template: 'application-status-update',
    data: { applicantName, jobTitle, status, statusMessage }
  });
};

const sendPasswordResetEmail = async (userEmail, fullName, resetLink) => {
  return sendEmail({
    to: userEmail,
    template: 'password-reset',
    data: { fullName, resetLink }
  });
};

module.exports = {
  sendEmail,
  sendBulkEmails,
  verifyEmailConfig,
  emailQueue,
  sendWelcomeEmail,
  sendApplicationConfirmation,
  sendApplicationStatusUpdate,
  sendPasswordResetEmail
};
