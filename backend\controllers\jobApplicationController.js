const { v4: uuidv4 } = require('uuid');
const { executeQuery, getCollection } = require('../config/database');
const { sendEmail } = require('../utils/emailService');

class JobApplicationController {
  // Submit job application
  async createApplication(req, res) {
    try {
      const { jobId, coverLetter, resumeUrl } = req.body;
      const userId = req.user.id;
      const userEmail = req.user.email;
      const userName = req.user.full_name;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if user has already applied for this job
      let existingApplication;
      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        existingApplication = await applicationsCollection.findOne({
          job_id: jobId,
          user_id: userId
        });
      } else {
        const applications = await executeQuery(
          'SELECT * FROM job_applications WHERE job_id = ? AND user_id = ?',
          [jobId, userId]
        );
        existingApplication = applications[0];
      }

      if (existingApplication) {
        return res.status(400).json({
          success: false,
          message: 'You have already applied for this job'
        });
      }

      // Check if job exists
      let job;
      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        job = await jobsCollection.findOne({ _id: jobId });
      } else {
        const jobs = await executeQuery('SELECT * FROM jobs WHERE id = ?', [jobId]);
        job = jobs[0];
      }

      if (!job) {
        return res.status(404).json({
          success: false,
          message: 'Job not found'
        });
      }

      if (job.status !== 'active') {
        return res.status(400).json({
          success: false,
          message: 'This job is no longer accepting applications'
        });
      }

      // Create application
      const applicationId = uuidv4();
      const applicationData = {
        id: applicationId,
        job_id: jobId,
        user_id: userId,
        applicant_name: userName,
        applicant_email: userEmail,
        cover_letter: coverLetter || null,
        resume_url: resumeUrl || null,
        status: 'pending',
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        await applicationsCollection.insertOne({ _id: applicationId, ...applicationData });
      } else {
        const insertQuery = `
          INSERT INTO job_applications (id, job_id, user_id, applicant_name, applicant_email,
                                       cover_letter, resume_url, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        await executeQuery(insertQuery, [
          applicationId, jobId, userId, userName, userEmail,
          coverLetter, resumeUrl, 'pending', new Date(), new Date()
        ]);
      }

      // Send confirmation email to applicant
      try {
        await sendEmail({
          to: userEmail,
          subject: 'Application Submitted Successfully',
          template: 'application-confirmation',
          data: {
            applicantName: userName,
            jobTitle: job.title,
            companyName: job.company_name || 'Company'
          }
        });
      } catch (emailError) {
        console.error('Failed to send confirmation email:', emailError);
      }

      res.status(201).json({
        success: true,
        message: 'Application submitted successfully',
        data: { application: { id: applicationId, ...applicationData } }
      });
    } catch (error) {
      console.error('Create application error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get applications by user
  async getUserApplications(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 10 } = req.query;
      const offset = (page - 1) * limit;
      const dbType = process.env.DB_TYPE || 'postgres';

      let applications, totalCount;

      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        const jobsCollection = await getCollection('jobs');
        const companiesCollection = await getCollection('companies');

        applications = await applicationsCollection
          .find({ user_id: userId })
          .sort({ created_at: -1 })
          .skip(offset)
          .limit(parseInt(limit))
          .toArray();

        // Get job and company details
        for (let application of applications) {
          const job = await jobsCollection.findOne({ _id: application.job_id });
          if (job) {
            application.job = job;
            if (job.company_id) {
              application.job.company = await companiesCollection.findOne({ _id: job.company_id });
            }
          }
        }

        totalCount = await applicationsCollection.countDocuments({ user_id: userId });
      } else {
        const query = `
          SELECT ja.*, j.title as job_title, j.location as job_location,
                 j.job_type, j.salary_min, j.salary_max, j.is_remote,
                 c.name as company_name, c.logo_url as company_logo
          FROM job_applications ja
          LEFT JOIN jobs j ON ja.job_id = j.id
          LEFT JOIN companies c ON j.company_id = c.id
          WHERE ja.user_id = ?
          ORDER BY ja.created_at DESC
          LIMIT ? OFFSET ?
        `;

        applications = await executeQuery(query, [userId, parseInt(limit), offset]);

        // Get total count
        const countResult = await executeQuery(
          'SELECT COUNT(*) as total FROM job_applications WHERE user_id = ?',
          [userId]
        );
        totalCount = countResult[0].total;

        // Format applications with job data
        applications = applications.map(app => ({
          ...app,
          job: app.job_title ? {
            id: app.job_id,
            title: app.job_title,
            location: app.job_location,
            job_type: app.job_type,
            salary_min: app.salary_min,
            salary_max: app.salary_max,
            is_remote: app.is_remote,
            company: app.company_name ? {
              name: app.company_name,
              logo_url: app.company_logo
            } : null
          } : null
        }));
      }

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          applications,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalCount,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });
    } catch (error) {
      console.error('Get user applications error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get applications for a job (for employers)
  async getJobApplications(req, res) {
    try {
      const { jobId } = req.params;
      const { page = 1, limit = 10, status } = req.query;
      const offset = (page - 1) * limit;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if user owns the job or is admin
      let job;
      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        job = await jobsCollection.findOne({ _id: jobId });
      } else {
        const jobs = await executeQuery('SELECT * FROM jobs WHERE id = ?', [jobId]);
        job = jobs[0];
      }

      if (!job) {
        return res.status(404).json({
          success: false,
          message: 'Job not found'
        });
      }

      if (job.posted_by !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to view applications for this job'
        });
      }

      let applications, totalCount;

      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        const profilesCollection = await getCollection('profiles');

        const filter = { job_id: jobId };
        if (status) filter.status = status;

        applications = await applicationsCollection
          .find(filter)
          .sort({ created_at: -1 })
          .skip(offset)
          .limit(parseInt(limit))
          .toArray();

        // Get profile details
        for (let application of applications) {
          const profile = await profilesCollection.findOne({ _id: application.user_id });
          if (profile) {
            application.profile = profile;
          }
        }

        totalCount = await applicationsCollection.countDocuments(filter);
      } else {
        let query = `
          SELECT ja.*, p.full_name, p.email, p.phone, p.location as profile_location,
                 p.bio, p.skills, p.experience_years, p.avatar_url
          FROM job_applications ja
          LEFT JOIN profiles p ON ja.user_id = p.id
          WHERE ja.job_id = ?
        `;

        let queryParams = [jobId];

        if (status) {
          query += ' AND ja.status = ?';
          queryParams.push(status);
        }

        query += ' ORDER BY ja.created_at DESC LIMIT ? OFFSET ?';
        queryParams.push(parseInt(limit), offset);

        applications = await executeQuery(query, queryParams);

        // Get total count
        let countQuery = 'SELECT COUNT(*) as total FROM job_applications WHERE job_id = ?';
        let countParams = [jobId];

        if (status) {
          countQuery += ' AND status = ?';
          countParams.push(status);
        }

        const countResult = await executeQuery(countQuery, countParams);
        totalCount = countResult[0].total;

        // Format applications with profile data
        applications = applications.map(app => ({
          ...app,
          profile: app.full_name ? {
            id: app.user_id,
            full_name: app.full_name,
            email: app.email,
            phone: app.phone,
            location: app.profile_location,
            bio: app.bio,
            skills: app.skills,
            experience_years: app.experience_years,
            avatar_url: app.avatar_url
          } : null
        }));
      }

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          applications,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalCount,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });
    } catch (error) {
      console.error('Get job applications error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update application status
  async updateApplicationStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Get application with job details
      let application;
      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        const jobsCollection = await getCollection('jobs');

        application = await applicationsCollection.findOne({ _id: id });
        if (application) {
          application.job = await jobsCollection.findOne({ _id: application.job_id });
        }
      } else {
        const query = `
          SELECT ja.*, j.posted_by, j.title as job_title
          FROM job_applications ja
          LEFT JOIN jobs j ON ja.job_id = j.id
          WHERE ja.id = ?
        `;

        const applications = await executeQuery(query, [id]);
        application = applications[0];
      }

      if (!application) {
        return res.status(404).json({
          success: false,
          message: 'Application not found'
        });
      }

      // Check if user owns the job or is admin
      const jobOwnerId = application.job?.posted_by || application.posted_by;
      if (jobOwnerId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to update this application'
        });
      }

      // Update application status
      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        await applicationsCollection.updateOne(
          { _id: id },
          { $set: { status, updated_at: new Date() } }
        );
      } else {
        await executeQuery(
          'UPDATE job_applications SET status = ?, updated_at = ? WHERE id = ?',
          [status, new Date(), id]
        );
      }

      // Send notification email to applicant
      try {
        await sendEmail({
          to: application.applicant_email,
          subject: `Application Status Update - ${application.job_title || 'Job Application'}`,
          template: 'application-status-update',
          data: {
            applicantName: application.applicant_name,
            jobTitle: application.job_title || 'Job',
            status,
            statusMessage: this.getStatusMessage(status)
          }
        });
      } catch (emailError) {
        console.error('Failed to send status update email:', emailError);
      }

      res.json({
        success: true,
        message: 'Application status updated successfully'
      });
    } catch (error) {
      console.error('Update application status error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get application statistics for user
  async getUserApplicationStats(req, res) {
    try {
      const userId = req.user.id;
      const dbType = process.env.DB_TYPE || 'postgres';

      let stats;

      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        
        const pipeline = [
          { $match: { user_id: userId } },
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 }
            }
          }
        ];

        const results = await applicationsCollection.aggregate(pipeline).toArray();
        
        stats = {
          total: 0,
          pending: 0,
          reviewed: 0,
          accepted: 0,
          rejected: 0
        };

        results.forEach(result => {
          stats[result._id] = result.count;
          stats.total += result.count;
        });
      } else {
        const query = `
          SELECT status, COUNT(*) as count
          FROM job_applications
          WHERE user_id = ?
          GROUP BY status
        `;

        const results = await executeQuery(query, [userId]);
        
        stats = {
          total: 0,
          pending: 0,
          reviewed: 0,
          accepted: 0,
          rejected: 0
        };

        results.forEach(result => {
          stats[result.status] = parseInt(result.count);
          stats.total += parseInt(result.count);
        });
      }

      res.json({
        success: true,
        data: { stats }
      });
    } catch (error) {
      console.error('Get user application stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get application statistics for a job
  async getJobApplicationStats(req, res) {
    try {
      const { jobId } = req.params;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if user owns the job or is admin
      let job;
      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        job = await jobsCollection.findOne({ _id: jobId });
      } else {
        const jobs = await executeQuery('SELECT * FROM jobs WHERE id = ?', [jobId]);
        job = jobs[0];
      }

      if (!job) {
        return res.status(404).json({
          success: false,
          message: 'Job not found'
        });
      }

      if (job.posted_by !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to view statistics for this job'
        });
      }

      let stats;

      if (dbType === 'mongodb') {
        const applicationsCollection = await getCollection('job_applications');
        
        const pipeline = [
          { $match: { job_id: jobId } },
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 }
            }
          }
        ];

        const results = await applicationsCollection.aggregate(pipeline).toArray();
        
        stats = {
          total: 0,
          pending: 0,
          reviewed: 0,
          accepted: 0,
          rejected: 0
        };

        results.forEach(result => {
          stats[result._id] = result.count;
          stats.total += result.count;
        });
      } else {
        const query = `
          SELECT status, COUNT(*) as count
          FROM job_applications
          WHERE job_id = ?
          GROUP BY status
        `;

        const results = await executeQuery(query, [jobId]);
        
        stats = {
          total: 0,
          pending: 0,
          reviewed: 0,
          accepted: 0,
          rejected: 0
        };

        results.forEach(result => {
          stats[result.status] = parseInt(result.count);
          stats.total += parseInt(result.count);
        });
      }

      res.json({
        success: true,
        data: { stats }
      });
    } catch (error) {
      console.error('Get job application stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Helper method to get status message
  getStatusMessage(status) {
    const messages = {
      pending: 'Your application is being reviewed.',
      reviewed: 'Your application has been reviewed.',
      accepted: 'Congratulations! Your application has been accepted.',
      rejected: 'Thank you for your interest. Unfortunately, we have decided to move forward with other candidates.'
    };

    return messages[status] || 'Your application status has been updated.';
  }
}

module.exports = new JobApplicationController();
