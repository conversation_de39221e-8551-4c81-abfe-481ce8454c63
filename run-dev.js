#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkRequirements() {
  log('\n🔍 Checking requirements...', 'yellow');
  
  // Check if we're in the right directory
  if (!fs.existsSync('backend') || !fs.existsSync('src')) {
    log('❌ Please run this script from the project root directory', 'red');
    log('Expected structure: backend/ and src/ folders', 'red');
    process.exit(1);
  }

  // Check if backend .env exists
  if (!fs.existsSync('backend/.env')) {
    log('❌ Backend .env file not found!', 'red');
    log('Please copy backend/.env.example to backend/.env and configure it:', 'yellow');
    log('  cd backend && cp .env.example .env', 'cyan');
    process.exit(1);
  }

  // Check if node_modules exist
  if (!fs.existsSync('node_modules')) {
    log('❌ Frontend dependencies not installed', 'red');
    log('Please run: npm install', 'cyan');
    process.exit(1);
  }

  if (!fs.existsSync('backend/node_modules')) {
    log('❌ Backend dependencies not installed', 'red');
    log('Please run: cd backend && npm install', 'cyan');
    process.exit(1);
  }

  log('✅ All requirements met', 'green');
}

function startServers() {
  log('\n🚀 Starting Career Link Spark Development Environment', 'bright');
  log('=====================================================', 'bright');
  log('Frontend: http://localhost:5173', 'cyan');
  log('Backend:  http://localhost:5000', 'magenta');
  log('API Docs: http://localhost:5000/api/docs', 'magenta');
  log('\nPress Ctrl+C to stop both servers\n', 'yellow');

  // Start backend
  const backendProcess = spawn('npm', ['run', 'dev'], {
    cwd: './backend',
    stdio: 'pipe',
    shell: true
  });

  // Start frontend
  const frontendProcess = spawn('npm', ['run', 'dev'], {
    cwd: '.',
    stdio: 'pipe',
    shell: true
  });

  // Handle backend output
  backendProcess.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      log(`[Backend] ${output}`, 'magenta');
    }
  });

  backendProcess.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output && !output.includes('ExperimentalWarning')) {
      log(`[Backend] ${output}`, 'red');
    }
  });

  // Handle frontend output
  frontendProcess.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      log(`[Frontend] ${output}`, 'cyan');
    }
  });

  frontendProcess.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output && !output.includes('ExperimentalWarning')) {
      log(`[Frontend] ${output}`, 'red');
    }
  });

  // Handle process termination
  const cleanup = () => {
    log('\n🛑 Shutting down servers...', 'yellow');
    backendProcess.kill('SIGTERM');
    frontendProcess.kill('SIGTERM');
    
    setTimeout(() => {
      backendProcess.kill('SIGKILL');
      frontendProcess.kill('SIGKILL');
      process.exit(0);
    }, 5000);
  };

  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);

  // Handle process errors
  backendProcess.on('error', (error) => {
    log(`❌ Backend error: ${error.message}`, 'red');
  });

  frontendProcess.on('error', (error) => {
    log(`❌ Frontend error: ${error.message}`, 'red');
  });

  // Handle process exit
  backendProcess.on('close', (code) => {
    if (code !== 0) {
      log(`❌ Backend process exited with code ${code}`, 'red');
    }
  });

  frontendProcess.on('close', (code) => {
    if (code !== 0) {
      log(`❌ Frontend process exited with code ${code}`, 'red');
    }
  });
}

// Main execution
try {
  checkRequirements();
  startServers();
} catch (error) {
  log(`❌ Error: ${error.message}`, 'red');
  process.exit(1);
}
