const { executeQuery, getCollection } = require('../config/database');
const { uploadToCloudinary } = require('../utils/fileUpload');

class ProfileController {
  // Get user profile
  async getProfile(req, res) {
    try {
      const { userId } = req.params;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if user can access this profile
      if (userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to access this profile'
        });
      }

      let profile;

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        profile = await profilesCollection.findOne({ _id: userId });
      } else {
        const profiles = await executeQuery('SELECT * FROM profiles WHERE id = ?', [userId]);
        profile = profiles[0];
      }

      if (!profile) {
        return res.status(404).json({
          success: false,
          message: 'Profile not found'
        });
      }

      res.json({
        success: true,
        data: { profile }
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get current user profile
  async getCurrentUserProfile(req, res) {
    try {
      const userId = req.user.id;
      const dbType = process.env.DB_TYPE || 'postgres';

      let profile;

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        profile = await profilesCollection.findOne({ _id: userId });
      } else {
        const profiles = await executeQuery('SELECT * FROM profiles WHERE id = ?', [userId]);
        profile = profiles[0];
      }

      if (!profile) {
        // Create profile if it doesn't exist
        const profileData = {
          id: userId,
          full_name: req.user.full_name,
          email: req.user.email,
          created_at: new Date(),
          updated_at: new Date()
        };

        if (dbType === 'mongodb') {
          const profilesCollection = await getCollection('profiles');
          await profilesCollection.insertOne({ _id: userId, ...profileData });
        } else {
          await executeQuery(
            'INSERT INTO profiles (id, full_name, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
            [userId, req.user.full_name, req.user.email, new Date(), new Date()]
          );
        }

        profile = profileData;
      }

      res.json({
        success: true,
        data: { profile }
      });
    } catch (error) {
      console.error('Get current user profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update profile
  async updateProfile(req, res) {
    try {
      const userId = req.user.id;
      const updateData = req.body;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Remove sensitive fields that shouldn't be updated via this endpoint
      delete updateData.id;
      delete updateData.created_at;

      updateData.updated_at = new Date();

      // Check if profile exists
      let existingProfile;
      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        existingProfile = await profilesCollection.findOne({ _id: userId });
      } else {
        const profiles = await executeQuery('SELECT * FROM profiles WHERE id = ?', [userId]);
        existingProfile = profiles[0];
      }

      if (!existingProfile) {
        // Create profile if it doesn't exist
        updateData.id = userId;
        updateData.created_at = new Date();

        if (dbType === 'mongodb') {
          const profilesCollection = await getCollection('profiles');
          await profilesCollection.insertOne({ _id: userId, ...updateData });
        } else {
          const fields = Object.keys(updateData);
          const values = Object.values(updateData);
          const placeholders = fields.map(() => '?').join(', ');

          await executeQuery(
            `INSERT INTO profiles (${fields.join(', ')}) VALUES (${placeholders})`,
            values
          );
        }
      } else {
        // Update existing profile
        if (dbType === 'mongodb') {
          const profilesCollection = await getCollection('profiles');
          await profilesCollection.updateOne({ _id: userId }, { $set: updateData });
        } else {
          const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
          const values = Object.values(updateData);
          values.push(userId);

          await executeQuery(`UPDATE profiles SET ${setClause} WHERE id = ?`, values);
        }
      }

      res.json({
        success: true,
        message: 'Profile updated successfully'
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Upload avatar
  async uploadAvatar(req, res) {
    try {
      const userId = req.user.id;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
      }

      // Upload to Cloudinary
      const result = await uploadToCloudinary(file.buffer, {
        folder: 'avatars',
        public_id: `avatar_${userId}`,
        transformation: [
          { width: 300, height: 300, crop: 'fill' },
          { quality: 'auto' }
        ]
      });

      // Update profile with new avatar URL
      const dbType = process.env.DB_TYPE || 'postgres';

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        await profilesCollection.updateOne(
          { _id: userId },
          { $set: { avatar_url: result.secure_url, updated_at: new Date() } }
        );
      } else {
        await executeQuery(
          'UPDATE profiles SET avatar_url = ?, updated_at = ? WHERE id = ?',
          [result.secure_url, new Date(), userId]
        );
      }

      res.json({
        success: true,
        message: 'Avatar uploaded successfully',
        data: { avatarUrl: result.secure_url }
      });
    } catch (error) {
      console.error('Upload avatar error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Upload resume
  async uploadResume(req, res) {
    try {
      const userId = req.user.id;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
      }

      // Upload to Cloudinary
      const result = await uploadToCloudinary(file.buffer, {
        folder: 'resumes',
        public_id: `resume_${userId}`,
        resource_type: 'auto'
      });

      // Update profile with new resume URL
      const dbType = process.env.DB_TYPE || 'postgres';

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        await profilesCollection.updateOne(
          { _id: userId },
          { $set: { resume_url: result.secure_url, updated_at: new Date() } }
        );
      } else {
        await executeQuery(
          'UPDATE profiles SET resume_url = ?, updated_at = ? WHERE id = ?',
          [result.secure_url, new Date(), userId]
        );
      }

      res.json({
        success: true,
        message: 'Resume uploaded successfully',
        data: { resumeUrl: result.secure_url }
      });
    } catch (error) {
      console.error('Upload resume error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Search profiles
  async searchProfiles(req, res) {
    try {
      const { query, page = 1, limit = 10 } = req.query;
      const offset = (page - 1) * limit;
      const dbType = process.env.DB_TYPE || 'postgres';

      if (!query) {
        return res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
      }

      let profiles, totalCount;

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');

        const filter = {
          $or: [
            { full_name: { $regex: query, $options: 'i' } },
            { bio: { $regex: query, $options: 'i' } },
            { location: { $regex: query, $options: 'i' } },
            { skills: { $in: [new RegExp(query, 'i')] } }
          ]
        };

        profiles = await profilesCollection
          .find(filter, { projection: { email: 0 } }) // Exclude email for privacy
          .sort({ updated_at: -1 })
          .skip(offset)
          .limit(parseInt(limit))
          .toArray();

        totalCount = await profilesCollection.countDocuments(filter);
      } else {
        const searchQuery = `
          SELECT id, full_name, bio, location, skills, experience_years, avatar_url, updated_at
          FROM profiles
          WHERE full_name ILIKE ? OR bio ILIKE ? OR location ILIKE ? OR skills::text ILIKE ?
          ORDER BY updated_at DESC
          LIMIT ? OFFSET ?
        `;

        const searchTerm = `%${query}%`;
        profiles = await executeQuery(searchQuery, [
          searchTerm, searchTerm, searchTerm, searchTerm,
          parseInt(limit), offset
        ]);

        const countQuery = `
          SELECT COUNT(*) as total
          FROM profiles
          WHERE full_name ILIKE ? OR bio ILIKE ? OR location ILIKE ? OR skills::text ILIKE ?
        `;

        const countResult = await executeQuery(countQuery, [
          searchTerm, searchTerm, searchTerm, searchTerm
        ]);
        totalCount = countResult[0].total;
      }

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          profiles,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalCount,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });
    } catch (error) {
      console.error('Search profiles error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get profile completion percentage
  async getProfileCompletion(req, res) {
    try {
      const userId = req.user.id;
      const dbType = process.env.DB_TYPE || 'postgres';

      let profile;

      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        profile = await profilesCollection.findOne({ _id: userId });
      } else {
        const profiles = await executeQuery('SELECT * FROM profiles WHERE id = ?', [userId]);
        profile = profiles[0];
      }

      if (!profile) {
        return res.json({
          success: true,
          data: { completion: 0 }
        });
      }

      // Calculate completion percentage
      const fields = [
        'full_name', 'email', 'phone', 'location', 'bio',
        'skills', 'experience_years', 'resume_url', 'avatar_url'
      ];

      const completedFields = fields.filter(field => {
        const value = profile[field];
        if (Array.isArray(value)) {
          return value.length > 0;
        }
        return value !== null && value !== undefined && value !== '';
      });

      const completion = Math.round((completedFields.length / fields.length) * 100);

      res.json({
        success: true,
        data: { completion }
      });
    } catch (error) {
      console.error('Get profile completion error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Delete profile
  async deleteProfile(req, res) {
    try {
      const userId = req.user.id;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if profile exists
      let existingProfile;
      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        existingProfile = await profilesCollection.findOne({ _id: userId });
      } else {
        const profiles = await executeQuery('SELECT * FROM profiles WHERE id = ?', [userId]);
        existingProfile = profiles[0];
      }

      if (!existingProfile) {
        return res.status(404).json({
          success: false,
          message: 'Profile not found'
        });
      }

      // Delete profile
      if (dbType === 'mongodb') {
        const profilesCollection = await getCollection('profiles');
        await profilesCollection.deleteOne({ _id: userId });
      } else {
        await executeQuery('DELETE FROM profiles WHERE id = ?', [userId]);
      }

      res.json({
        success: true,
        message: 'Profile deleted successfully'
      });
    } catch (error) {
      console.error('Delete profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}

module.exports = new ProfileController();
