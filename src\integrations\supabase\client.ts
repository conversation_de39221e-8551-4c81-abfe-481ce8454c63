// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wbcssqxpoiryqoeghhhp.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndiY3NzcXhwb2lyeXFvZWdoaGhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODQ1OTQsImV4cCI6MjA2NDM2MDU5NH0.7RzN7-RKLsBYrXIVZPrjSYuu4kx-isyixcQJqcPRLvY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);