const { v4: uuidv4 } = require('uuid');
const { executeQuery, getCollection } = require('../config/database');

class CompanyController {
  // Get all companies with pagination and search
  async getCompanies(req, res) {
    try {
      const { page = 1, limit = 10, search } = req.query;
      const offset = (page - 1) * limit;
      const dbType = process.env.DB_TYPE || 'postgres';

      let companies, totalCount;

      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');

        const filter = {};
        if (search) {
          filter.$or = [
            { name: { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } }
          ];
        }

        companies = await companiesCollection
          .find(filter)
          .sort({ name: 1 })
          .skip(offset)
          .limit(parseInt(limit))
          .toArray();

        totalCount = await companiesCollection.countDocuments(filter);
      } else {
        let query = 'SELECT * FROM companies';
        let countQuery = 'SELECT COUNT(*) as total FROM companies';
        let queryParams = [];

        if (search) {
          query += ' WHERE name ILIKE ? OR description ILIKE ?';
          countQuery += ' WHERE name ILIKE ? OR description ILIKE ?';
          queryParams = [`%${search}%`, `%${search}%`];
        }

        query += ' ORDER BY name ASC LIMIT ? OFFSET ?';
        queryParams.push(parseInt(limit), offset);

        companies = await executeQuery(query, queryParams);

        const countParams = search ? [`%${search}%`, `%${search}%`] : [];
        const countResult = await executeQuery(countQuery, countParams);
        totalCount = countResult[0].total;
      }

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          companies,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalCount,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });
    } catch (error) {
      console.error('Get companies error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get single company by ID
  async getCompanyById(req, res) {
    try {
      const { id } = req.params;
      const dbType = process.env.DB_TYPE || 'postgres';

      let company;

      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        company = await companiesCollection.findOne({ _id: id });
      } else {
        const companies = await executeQuery('SELECT * FROM companies WHERE id = ?', [id]);
        company = companies[0];
      }

      if (!company) {
        return res.status(404).json({
          success: false,
          message: 'Company not found'
        });
      }

      res.json({
        success: true,
        data: { company }
      });
    } catch (error) {
      console.error('Get company by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Create new company
  async createCompany(req, res) {
    try {
      const { name, description, website, logoUrl, location } = req.body;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if company with same name already exists
      let existingCompany;
      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        existingCompany = await companiesCollection.findOne({ name });
      } else {
        const companies = await executeQuery('SELECT * FROM companies WHERE name = ?', [name]);
        existingCompany = companies[0];
      }

      if (existingCompany) {
        return res.status(400).json({
          success: false,
          message: 'A company with this name already exists'
        });
      }

      // Create company
      const companyId = uuidv4();
      const companyData = {
        id: companyId,
        name,
        description: description || null,
        website: website || null,
        logo_url: logoUrl || null,
        location: location || null,
        created_at: new Date(),
        updated_at: new Date()
      };

      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        await companiesCollection.insertOne({ _id: companyId, ...companyData });
      } else {
        const insertQuery = `
          INSERT INTO companies (id, name, description, website, logo_url, location, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;

        await executeQuery(insertQuery, [
          companyId, name, description, website, logoUrl, location,
          new Date(), new Date()
        ]);
      }

      res.status(201).json({
        success: true,
        message: 'Company created successfully',
        data: { company: { id: companyId, ...companyData } }
      });
    } catch (error) {
      console.error('Create company error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update company
  async updateCompany(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if company exists
      let existingCompany;
      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        existingCompany = await companiesCollection.findOne({ _id: id });
      } else {
        const companies = await executeQuery('SELECT * FROM companies WHERE id = ?', [id]);
        existingCompany = companies[0];
      }

      if (!existingCompany) {
        return res.status(404).json({
          success: false,
          message: 'Company not found'
        });
      }

      // If name is being updated, check for duplicates
      if (updateData.name && updateData.name !== existingCompany.name) {
        let duplicateCompany;
        if (dbType === 'mongodb') {
          const companiesCollection = await getCollection('companies');
          duplicateCompany = await companiesCollection.findOne({ 
            name: updateData.name,
            _id: { $ne: id }
          });
        } else {
          const companies = await executeQuery(
            'SELECT * FROM companies WHERE name = ? AND id != ?',
            [updateData.name, id]
          );
          duplicateCompany = companies[0];
        }

        if (duplicateCompany) {
          return res.status(400).json({
            success: false,
            message: 'A company with this name already exists'
          });
        }
      }

      // Update company
      updateData.updated_at = new Date();

      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        await companiesCollection.updateOne({ _id: id }, { $set: updateData });
      } else {
        const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const values = Object.values(updateData);
        values.push(id);

        await executeQuery(`UPDATE companies SET ${setClause} WHERE id = ?`, values);
      }

      res.json({
        success: true,
        message: 'Company updated successfully'
      });
    } catch (error) {
      console.error('Update company error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Delete company
  async deleteCompany(req, res) {
    try {
      const { id } = req.params;
      const dbType = process.env.DB_TYPE || 'postgres';

      // Check if company exists
      let existingCompany;
      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        existingCompany = await companiesCollection.findOne({ _id: id });
      } else {
        const companies = await executeQuery('SELECT * FROM companies WHERE id = ?', [id]);
        existingCompany = companies[0];
      }

      if (!existingCompany) {
        return res.status(404).json({
          success: false,
          message: 'Company not found'
        });
      }

      // Check if company has associated jobs
      let hasJobs;
      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        const jobCount = await jobsCollection.countDocuments({ company_id: id });
        hasJobs = jobCount > 0;
      } else {
        const jobs = await executeQuery('SELECT COUNT(*) as count FROM jobs WHERE company_id = ?', [id]);
        hasJobs = jobs[0].count > 0;
      }

      if (hasJobs) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete company with associated jobs'
        });
      }

      // Delete company
      if (dbType === 'mongodb') {
        const companiesCollection = await getCollection('companies');
        await companiesCollection.deleteOne({ _id: id });
      } else {
        await executeQuery('DELETE FROM companies WHERE id = ?', [id]);
      }

      res.json({
        success: true,
        message: 'Company deleted successfully'
      });
    } catch (error) {
      console.error('Delete company error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get jobs for a company
  async getCompanyJobs(req, res) {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10 } = req.query;
      const offset = (page - 1) * limit;
      const dbType = process.env.DB_TYPE || 'postgres';

      let jobs, totalCount;

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');

        jobs = await jobsCollection
          .find({ company_id: id })
          .sort({ created_at: -1 })
          .skip(offset)
          .limit(parseInt(limit))
          .toArray();

        totalCount = await jobsCollection.countDocuments({ company_id: id });
      } else {
        const query = `
          SELECT * FROM jobs
          WHERE company_id = ?
          ORDER BY created_at DESC
          LIMIT ? OFFSET ?
        `;

        jobs = await executeQuery(query, [id, parseInt(limit), offset]);

        const countResult = await executeQuery(
          'SELECT COUNT(*) as total FROM jobs WHERE company_id = ?',
          [id]
        );
        totalCount = countResult[0].total;
      }

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          jobs,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalCount,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });
    } catch (error) {
      console.error('Get company jobs error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get company statistics
  async getCompanyStats(req, res) {
    try {
      const { id } = req.params;
      const dbType = process.env.DB_TYPE || 'postgres';

      let stats;

      if (dbType === 'mongodb') {
        const jobsCollection = await getCollection('jobs');
        const applicationsCollection = await getCollection('job_applications');

        // Get job counts
        const totalJobs = await jobsCollection.countDocuments({ company_id: id });
        const activeJobs = await jobsCollection.countDocuments({ 
          company_id: id, 
          status: 'active' 
        });

        // Get job IDs for this company
        const companyJobs = await jobsCollection
          .find({ company_id: id }, { projection: { _id: 1 } })
          .toArray();
        
        const jobIds = companyJobs.map(job => job._id);

        // Get application count
        const totalApplications = jobIds.length > 0 
          ? await applicationsCollection.countDocuments({ job_id: { $in: jobIds } })
          : 0;

        stats = {
          totalJobs,
          activeJobs,
          totalApplications
        };
      } else {
        // Get job counts
        const jobStatsQuery = `
          SELECT 
            COUNT(*) as total_jobs,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_jobs
          FROM jobs
          WHERE company_id = ?
        `;

        const jobStats = await executeQuery(jobStatsQuery, [id]);

        // Get application count
        const applicationStatsQuery = `
          SELECT COUNT(ja.id) as total_applications
          FROM job_applications ja
          INNER JOIN jobs j ON ja.job_id = j.id
          WHERE j.company_id = ?
        `;

        const applicationStats = await executeQuery(applicationStatsQuery, [id]);

        stats = {
          totalJobs: parseInt(jobStats[0].total_jobs),
          activeJobs: parseInt(jobStats[0].active_jobs),
          totalApplications: parseInt(applicationStats[0].total_applications)
        };
      }

      res.json({
        success: true,
        data: { stats }
      });
    } catch (error) {
      console.error('Get company stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}

module.exports = new CompanyController();
