version: '3.8'

services:
  # Backend API
  backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - PORT=5000
      - DB_TYPE=postgres
      - DATABASE_URL=********************************************/career_link_spark
      - JWT_SECRET=your-super-secret-jwt-key-for-development
      - JWT_EXPIRES_IN=7d
      - FRONTEND_URL=http://localhost:3000
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
    depends_on:
      - postgres
      - redis
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - career-link-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=career_link_spark
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - career-link-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - career-link-network

  # MySQL (alternative database)
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=career_link_spark
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - career-link-network
    profiles:
      - mysql

  # MongoDB (alternative database)
  mongodb:
    image: mongo:6.0
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=career_link_spark
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - career-link-network
    profiles:
      - mongodb

  # Nginx (reverse proxy)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - career-link-network
    profiles:
      - production

volumes:
  postgres_data:
  mysql_data:
  mongodb_data:
  redis_data:

networks:
  career-link-network:
    driver: bridge
