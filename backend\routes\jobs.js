const express = require('express');
const router = express.Router();
const jobController = require('../controllers/jobController');
const { verifyToken, requireEmployer, optionalAuth } = require('../middleware/auth');
const {
  validateJobCreation,
  validateJobUpdate,
  validatePagination,
  validateJobFilters
} = require('../middleware/validation');

// @route   GET /api/jobs
// @desc    Get all jobs with filtering and pagination
// @access  Public
router.get('/', validatePagination, validateJobFilters, jobController.getJobs);

// @route   GET /api/jobs/featured
// @desc    Get featured jobs
// @access  Public
router.get('/featured', jobController.getFeaturedJobs);

// @route   GET /api/jobs/:id
// @desc    Get single job by ID
// @access  Public
router.get('/:id', optionalAuth, jobController.getJobById);

// @route   POST /api/jobs
// @desc    Create new job
// @access  Private (Employer/Admin)
router.post('/', verifyToken, requireEmployer, validateJobCreation, jobController.createJob);

// @route   PUT /api/jobs/:id
// @desc    Update job
// @access  Private (Job Owner/Admin)
router.put('/:id', verifyToken, validateJobUpdate, jobController.updateJob);

// @route   DELETE /api/jobs/:id
// @desc    Delete job
// @access  Private (Job Owner/Admin)
router.delete('/:id', verifyToken, jobController.deleteJob);

// @route   GET /api/jobs/user/my-jobs
// @desc    Get jobs posted by current user
// @access  Private (Employer/Admin)
router.get('/user/my-jobs', verifyToken, requireEmployer, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    const userId = req.user.id;
    const { executeQuery, getCollection } = require('../config/database');
    const dbType = process.env.DB_TYPE || 'postgres';

    let jobs, totalCount;

    if (dbType === 'mongodb') {
      const jobsCollection = await getCollection('jobs');
      const companiesCollection = await getCollection('companies');

      jobs = await jobsCollection
        .find({ posted_by: userId })
        .sort({ created_at: -1 })
        .skip(offset)
        .limit(parseInt(limit))
        .toArray();

      // Get company details
      for (let job of jobs) {
        if (job.company_id) {
          job.company = await companiesCollection.findOne({ _id: job.company_id });
        }
      }

      totalCount = await jobsCollection.countDocuments({ posted_by: userId });
    } else {
      const query = `
        SELECT j.*, c.name as company_name, c.description as company_description,
               c.website as company_website, c.logo_url as company_logo_url,
               c.location as company_location
        FROM jobs j
        LEFT JOIN companies c ON j.company_id = c.id
        WHERE j.posted_by = ?
        ORDER BY j.created_at DESC
        LIMIT ? OFFSET ?
      `;

      jobs = await executeQuery(query, [userId, parseInt(limit), offset]);

      const countResult = await executeQuery(
        'SELECT COUNT(*) as total FROM jobs WHERE posted_by = ?',
        [userId]
      );
      totalCount = countResult[0].total;

      // Format jobs with company data
      jobs = jobs.map(job => ({
        ...job,
        company: job.company_name ? {
          id: job.company_id,
          name: job.company_name,
          description: job.company_description,
          website: job.company_website,
          logo_url: job.company_logo_url,
          location: job.company_location
        } : null
      }));
    }

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      success: true,
      data: {
        jobs,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get my jobs error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   GET /api/jobs/:id/stats
// @desc    Get job statistics (applications count, etc.)
// @access  Private (Job Owner/Admin)
router.get('/:id/stats', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { executeQuery, getCollection } = require('../config/database');
    const dbType = process.env.DB_TYPE || 'postgres';

    // Check if user owns the job or is admin
    let job;
    if (dbType === 'mongodb') {
      const jobsCollection = await getCollection('jobs');
      job = await jobsCollection.findOne({ _id: id });
    } else {
      const jobs = await executeQuery('SELECT * FROM jobs WHERE id = ?', [id]);
      job = jobs[0];
    }

    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    if (job.posted_by !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view statistics for this job'
      });
    }

    let stats;

    if (dbType === 'mongodb') {
      const applicationsCollection = await getCollection('job_applications');
      
      const pipeline = [
        { $match: { job_id: id } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ];

      const results = await applicationsCollection.aggregate(pipeline).toArray();
      
      stats = {
        total: 0,
        pending: 0,
        reviewed: 0,
        accepted: 0,
        rejected: 0
      };

      results.forEach(result => {
        stats[result._id] = result.count;
        stats.total += result.count;
      });
    } else {
      const query = `
        SELECT status, COUNT(*) as count
        FROM job_applications
        WHERE job_id = ?
        GROUP BY status
      `;

      const results = await executeQuery(query, [id]);
      
      stats = {
        total: 0,
        pending: 0,
        reviewed: 0,
        accepted: 0,
        rejected: 0
      };

      results.forEach(result => {
        stats[result.status] = parseInt(result.count);
        stats.total += parseInt(result.count);
      });
    }

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error) {
    console.error('Get job stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   POST /api/jobs/:id/duplicate
// @desc    Duplicate a job posting
// @access  Private (Job Owner/Admin)
router.post('/:id/duplicate', verifyToken, requireEmployer, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { v4: uuidv4 } = require('uuid');
    const { executeQuery, getCollection } = require('../config/database');
    const dbType = process.env.DB_TYPE || 'postgres';

    // Get original job
    let originalJob;
    if (dbType === 'mongodb') {
      const jobsCollection = await getCollection('jobs');
      originalJob = await jobsCollection.findOne({ _id: id });
    } else {
      const jobs = await executeQuery('SELECT * FROM jobs WHERE id = ?', [id]);
      originalJob = jobs[0];
    }

    if (!originalJob) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    if (originalJob.posted_by !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to duplicate this job'
      });
    }

    // Create duplicate job
    const newJobId = uuidv4();
    const duplicateJob = {
      ...originalJob,
      id: newJobId,
      title: `${originalJob.title} (Copy)`,
      status: 'active',
      created_at: new Date(),
      updated_at: new Date()
    };

    // Remove original ID fields
    delete duplicateJob._id;

    if (dbType === 'mongodb') {
      const jobsCollection = await getCollection('jobs');
      await jobsCollection.insertOne({ _id: newJobId, ...duplicateJob });
    } else {
      const fields = Object.keys(duplicateJob);
      const values = Object.values(duplicateJob);
      const placeholders = fields.map(() => '?').join(', ');

      await executeQuery(
        `INSERT INTO jobs (${fields.join(', ')}) VALUES (${placeholders})`,
        values
      );
    }

    res.status(201).json({
      success: true,
      message: 'Job duplicated successfully',
      data: { job: { id: newJobId, ...duplicateJob } }
    });
  } catch (error) {
    console.error('Duplicate job error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
