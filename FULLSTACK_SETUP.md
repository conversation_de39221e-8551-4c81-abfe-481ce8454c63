# 🚀 Career Link Spark - Full Stack Setup Guide

This guide will help you set up and run both the frontend and backend of the Career Link Spark job portal application.

## 📋 Prerequisites

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **Database** (choose one):
  - PostgreSQL (recommended)
  - MySQL
  - MongoDB
- **Git** - [Download here](https://git-scm.com/)

## 🏗️ Project Structure

```
career-link-spark/
├── src/                    # Frontend React application
├── backend/               # Backend Node.js API
├── start-dev.js          # Development startup script
├── start-dev.sh          # Linux/Mac startup script
├── start-dev.bat         # Windows startup script
├── package.json          # Frontend dependencies
└── FULLSTACK_SETUP.md    # This file
```

## ⚡ Quick Start (Recommended)

### 1. **Clone and Navigate**
```bash
git clone <your-repository-url>
cd career-link-spark
```

### 2. **Run Setup Script**

**Windows:**
```cmd
start-dev.bat
```

**Linux/Mac:**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

**Cross-platform (Node.js):**
```bash
node start-dev.js
```

The script will:
- ✅ Check Node.js installation
- 📦 Install frontend and backend dependencies
- ⚙️ Verify backend configuration
- 🚀 Start both servers simultaneously

### 3. **Access the Application**
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api/docs

## 🔧 Manual Setup (Alternative)

If you prefer to set up manually or the scripts don't work:

### 1. **Install Frontend Dependencies**
```bash
npm install
```

### 2. **Install Backend Dependencies**
```bash
cd backend
npm install
cd ..
```

### 3. **Configure Backend Environment**
```bash
cd backend
cp .env.example .env
# Edit .env file with your configuration
nano .env  # or use your preferred editor
cd ..
```

### 4. **Setup Database**
```bash
cd backend
npm run setup  # This runs migrations and adds sample data
cd ..
```

### 5. **Start Development Servers**

**Option A: Using concurrently (if installed)**
```bash
# Install concurrently globally
npm install -g concurrently

# Start both servers
concurrently "npm run dev" "cd backend && npm run dev"
```

**Option B: Separate terminals**

Terminal 1 (Frontend):
```bash
npm run dev
```

Terminal 2 (Backend):
```bash
cd backend
npm run dev
```

## 🗄️ Database Configuration

### PostgreSQL (Recommended)
```env
DB_TYPE=postgres
DATABASE_URL=postgresql://username:password@localhost:5432/career_link_spark
```

### MySQL
```env
DB_TYPE=mysql
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=career_link_spark
```

### MongoDB
```env
DB_TYPE=mongodb
MONGODB_URI=mongodb://localhost:27017/career_link_spark
```

## 🔑 Environment Variables

Create `backend/.env` file with these variables:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database (choose one configuration above)
DB_TYPE=postgres
DATABASE_URL=postgresql://username:password@localhost:5432/career_link_spark

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-for-development
JWT_EXPIRES_IN=7d

# Email Configuration (optional for development)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# File Upload Configuration (optional)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Frontend Configuration
FRONTEND_URL=http://localhost:5173
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 📊 Sample Data

After running the setup, you can use these sample accounts:

- **Admin**: <EMAIL> / Admin123!
- **Employer**: <EMAIL> / Employer123!
- **User**: <EMAIL> / User123!

## 🛠️ Available Scripts

### Root Level Scripts
```bash
# Start both frontend and backend in development mode
npm run dev

# Install all dependencies (frontend + backend)
npm run install:all

# Setup backend database
npm run backend:setup

# Build frontend for production
npm run build
```

### Frontend Scripts (from root)
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm test             # Run tests
```

### Backend Scripts (from backend folder)
```bash
cd backend
npm run dev          # Start development server
npm start            # Start production server
npm run migrate      # Run database migrations
npm run seed         # Add sample data
npm run setup        # Run migrations + seed data
npm test             # Run tests
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Kill process using port 5000 (backend)
   npx kill-port 5000
   
   # Kill process using port 5173 (frontend)
   npx kill-port 5173
   ```

2. **Database Connection Error**
   - Check if your database server is running
   - Verify DATABASE_URL in backend/.env
   - Ensure database exists

3. **Module Not Found Errors**
   ```bash
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   
   cd backend
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **Permission Denied (Linux/Mac)**
   ```bash
   chmod +x start-dev.sh
   chmod +x start-dev.js
   ```

5. **Backend .env Not Found**
   ```bash
   cd backend
   cp .env.example .env
   # Edit the .env file with your configuration
   ```

### Development Tips

1. **API Testing**
   - Use the built-in API documentation: http://localhost:5000/api/docs
   - Test endpoints with tools like Postman or curl

2. **Database Management**
   ```bash
   cd backend
   npm run migrate      # Create/update tables
   npm run seed         # Add sample data
   ```

3. **Logs and Debugging**
   - Frontend logs appear in browser console
   - Backend logs appear in terminal
   - Check Network tab in browser dev tools for API calls

## 🚀 Production Deployment

### Frontend (Vite Build)
```bash
npm run build
# Deploy the 'dist' folder to your hosting service
```

### Backend
```bash
cd backend
npm ci --only=production
npm start
# Or use PM2 for process management
```

## 📚 Additional Resources

- **Frontend Documentation**: Check src/README.md
- **Backend Documentation**: Check backend/README.md
- **API Documentation**: http://localhost:5000/api/docs (when running)
- **Database Schema**: Check backend/scripts/migrate.js

## 🆘 Getting Help

If you encounter issues:

1. Check the console/terminal for error messages
2. Verify all environment variables are set correctly
3. Ensure your database is running and accessible
4. Check that all dependencies are installed
5. Try restarting both servers

## 🎉 Success!

If everything is working correctly, you should see:

- ✅ Frontend running at http://localhost:5173
- ✅ Backend API running at http://localhost:5000
- ✅ Database connected and migrations completed
- ✅ Sample data loaded (if you ran the seed script)

You're now ready to develop the Career Link Spark job portal! 🚀
